# FWFC (Finn Wolfhard Fan Community) Code Review Prompt

## Overview
This prompt guides comprehensive code reviews for the FWFC website, a SvelteKit-based fan community platform designed to serve users with intellectual disabilities through enhanced safety features, AI content management, and strict accessibility compliance.

## Review Checklist

### 🏗️ Technical Architecture & Code Quality

#### SvelteKit Application Structure
- [ ] **Route Organization**: Verify logical route structure with proper `+page.svelte`, `+page.server.ts`, and `+layout.svelte` files
- [ ] **Component Hierarchy**: Check component organization in `src/lib/components/` with proper separation of concerns
- [ ] **Server-Side Rendering**: Ensure proper SSR implementation with data loading in `+page.server.ts` files
- [ ] **API Route Design**: Validate RESTful API structure in `src/routes/api/` with consistent response formats
- [ ] **Error Boundaries**: Verify error handling at route and component levels with proper user feedback

#### TypeScript Implementation
- [ ] **Type Safety**: Check for proper TypeScript usage with interfaces, types, and generic constraints
- [ ] **Database Schema Types**: Verify Drizzle ORM schema definitions match database structure
- [ ] **API Response Types**: Ensure consistent typing for API responses and error handling
- [ ] **Component Props**: Validate proper prop typing with required/optional distinctions
- [ ] **Store Typing**: Check Svelte store implementations with proper TypeScript integration

#### Database Schema Design (Drizzle ORM + SQLite)
- [ ] **Schema Consistency**: Verify schema definitions in `src/lib/server/db/schema.ts` match requirements
- [ ] **Foreign Key Relationships**: Check proper relationships between users, content, and audit tables
- [ ] **Index Optimization**: Ensure proper indexing for frequently queried columns
- [ ] **Migration Scripts**: Verify database migrations are properly structured and reversible
- [ ] **Data Integrity**: Check constraints, defaults, and validation rules

#### Component Architecture & Reusability
- [ ] **Component Composition**: Verify proper component breakdown with single responsibility principle
- [ ] **Prop Interface Design**: Check consistent prop patterns across similar components
- [ ] **Slot Usage**: Ensure proper use of Svelte slots for flexible component composition
- [ ] **Event Handling**: Verify consistent event dispatching and handling patterns
- [ ] **Style Encapsulation**: Check component-scoped styles and CSS custom property usage

### 🔒 Security & Safety Features

#### AI Content Generation & Review System
- [ ] **Content Authenticity Scoring**: Verify implementation of AI content scoring algorithms
- [ ] **Review Workflow**: Check multi-stage content review process (AI → Manual → Approval)
- [ ] **Content Authorship Tracking**: Ensure proper tracking in `content_authorship` table
- [ ] **Rate Limiting**: Verify AI operation rate limiting to prevent abuse
- [ ] **Content Flagging**: Check automated content flagging and manual review triggers

#### User Authentication & Role-Based Access Control
- [ ] **Session Management**: Verify secure session handling with HTTP-only cookies
- [ ] **Password Security**: Check PBKDF2 implementation with proper salt generation
- [ ] **Role Enforcement**: Verify admin/moderator/user role restrictions throughout application
- [ ] **CSRF Protection**: Ensure CSRF token validation on state-changing operations
- [ ] **Input Sanitization**: Check XSS prevention through proper input sanitization

#### Rate Limiting & Abuse Prevention
- [ ] **API Rate Limits**: Verify rate limiting implementation on all API endpoints
- [ ] **User Action Throttling**: Check limits on content creation, comments, and interactions
- [ ] **CAPTCHA Integration**: Verify math-based CAPTCHA implementation for registration
- [ ] **Suspicious Activity Detection**: Check monitoring for unusual user behavior patterns
- [ ] **Account Lockout**: Verify temporary lockout mechanisms for repeated failures

### ♿ Accessibility Compliance (WCAG 2.1 AA)

#### Theme System Implementation
- [ ] **Color Contrast Ratios**: Verify minimum 4.5:1 contrast for normal text, 3:1 for large text
- [ ] **Dark Theme Compliance**: Check enhanced dark theme colors meet accessibility standards
- [ ] **High Contrast Mode**: Verify support for `prefers-contrast: high` media query
- [ ] **Theme Persistence**: Check localStorage integration and system preference detection
- [ ] **CSS Custom Properties**: Verify proper theme token implementation

#### Screen Reader Compatibility
- [ ] **ARIA Labels**: Check comprehensive ARIA labeling for all interactive elements
- [ ] **ARIA Live Regions**: Verify dynamic content announcements for loading states
- [ ] **Semantic HTML**: Ensure proper heading hierarchy (h1→h2→h3) and landmark usage
- [ ] **Alt Text**: Check descriptive alternative text for all images and media
- [ ] **Form Labels**: Verify all form inputs have associated labels or aria-label attributes

#### Keyboard Navigation & Focus Management
- [ ] **Skip Links**: Verify "Skip to main content" and navigation skip links
- [ ] **Tab Order**: Check logical tab sequence through all interactive elements
- [ ] **Focus Indicators**: Ensure high-contrast focus outlines with proper visibility
- [ ] **Keyboard Shortcuts**: Verify escape key support for dismissible elements
- [ ] **Focus Trapping**: Check proper focus management in modals and dialogs

#### Motion & Animation Accessibility
- [ ] **Reduced Motion Support**: Verify `prefers-reduced-motion: reduce` implementation
- [ ] **Animation Controls**: Check that animations can be disabled or reduced
- [ ] **Transition Management**: Ensure smooth but not overwhelming visual transitions
- [ ] **Loading Indicators**: Verify accessible loading states with proper announcements

### 🤖 AI Content Management System

#### Content Review Workflow
- [ ] **Multi-Stage Review**: Verify AI screening → human review → approval workflow
- [ ] **Authenticity Scoring**: Check implementation of content authenticity algorithms
- [ ] **Review Queue Management**: Verify proper queuing and assignment of content for review
- [ ] **Approval/Rejection Logic**: Check proper status transitions and notifications
- [ ] **Content Versioning**: Verify tracking of content changes and review history

#### Admin Content Management
- [ ] **Post as User Feature**: Verify admin ability to create content as any user
- [ ] **Content Scheduling**: Check scheduling system with background job processing
- [ ] **Bulk Operations**: Verify efficient handling of multiple content items
- [ ] **Simulated User Interactions**: Check authentic-looking interaction generation
- [ ] **Content Authorship Transparency**: Ensure clear tracking of admin-generated content

#### Audit Trail & Logging
- [ ] **Comprehensive Logging**: Verify all admin actions are logged with full context
- [ ] **Risk Assessment**: Check categorization of actions by risk level (low/medium/high)
- [ ] **IP Address Tracking**: Verify IP logging for security monitoring
- [ ] **Data Export**: Check audit log export functionality in multiple formats
- [ ] **Log Retention**: Verify proper log retention and cleanup policies

### ⚡ Performance & Scalability

#### Database Query Optimization
- [ ] **Query Efficiency**: Check for N+1 query problems and proper eager loading
- [ ] **Index Usage**: Verify queries utilize appropriate database indexes
- [ ] **Pagination**: Check efficient pagination implementation for large datasets
- [ ] **Connection Pooling**: Verify proper database connection management
- [ ] **Query Monitoring**: Check for slow query identification and optimization

#### Caching Strategies
- [ ] **Static Asset Caching**: Verify proper cache headers for images and static files
- [ ] **API Response Caching**: Check appropriate caching for frequently accessed data
- [ ] **Browser Caching**: Verify proper cache-control headers and ETags
- [ ] **CDN Integration**: Check readiness for CDN deployment if applicable
- [ ] **Cache Invalidation**: Verify proper cache busting for updated content

#### Image Optimization & Responsive Design
- [ ] **Image Compression**: Check automatic image optimization and compression
- [ ] **Responsive Images**: Verify proper srcset and sizes attributes
- [ ] **Lazy Loading**: Check implementation of progressive image loading
- [ ] **WebP Support**: Verify modern image format support with fallbacks
- [ ] **Mobile Performance**: Check image loading performance on mobile devices

#### Server-Side Rendering Performance
- [ ] **SSR Optimization**: Verify efficient server-side rendering implementation
- [ ] **Hydration**: Check proper client-side hydration without layout shifts
- [ ] **Code Splitting**: Verify automatic code splitting and lazy loading
- [ ] **Bundle Size**: Check JavaScript bundle sizes and optimization
- [ ] **Critical CSS**: Verify critical CSS inlining for faster initial renders

### 📁 Code Organization & Maintainability

#### File Structure & Naming Conventions
- [ ] **Directory Organization**: Check logical grouping of files and consistent structure
- [ ] **Naming Consistency**: Verify consistent naming patterns for files and components
- [ ] **Import Organization**: Check clean import statements with proper path resolution
- [ ] **Component Placement**: Verify components are in appropriate directories
- [ ] **Utility Organization**: Check proper separation of utilities and helpers

#### Error Handling & Logging
- [ ] **Error Boundaries**: Verify proper error catching and user-friendly error messages
- [ ] **Logging Strategy**: Check comprehensive logging for debugging and monitoring
- [ ] **Error Recovery**: Verify graceful degradation and recovery mechanisms
- [ ] **User Feedback**: Check proper error communication to users
- [ ] **Development vs Production**: Verify appropriate error handling for each environment

#### Documentation Quality
- [ ] **Code Comments**: Check meaningful comments for complex logic and business rules
- [ ] **README Documentation**: Verify comprehensive setup and usage instructions
- [ ] **API Documentation**: Check proper documentation of API endpoints and responses
- [ ] **Component Documentation**: Verify prop and event documentation for components
- [ ] **Architecture Documentation**: Check high-level system architecture documentation

#### Test Coverage & Testing Strategies
- [ ] **Unit Test Coverage**: Verify comprehensive unit tests for critical functions
- [ ] **Integration Tests**: Check API endpoint and database integration testing
- [ ] **Accessibility Tests**: Verify automated accessibility testing implementation
- [ ] **E2E Tests**: Check end-to-end testing for critical user workflows
- [ ] **Test Organization**: Verify logical test file organization and naming

## Special Considerations for FWFC

### 🧠 Intellectual Disability Support
- [ ] **Simplified Interface Options**: Check implementation of simplified UI modes
- [ ] **Clear Language**: Verify use of plain language and clear instructions
- [ ] **Error Prevention**: Check proactive error prevention and clear recovery paths
- [ ] **Consistent Navigation**: Verify predictable and consistent navigation patterns
- [ ] **Help and Support**: Check availability of contextual help and support features

### 🛡️ Enhanced Safety Features
- [ ] **Content Moderation**: Verify robust content moderation and filtering systems
- [ ] **User Reporting**: Check easy-to-use reporting mechanisms for inappropriate content
- [ ] **Safe Interaction**: Verify measures to prevent harassment and ensure safe interactions
- [ ] **Privacy Protection**: Check strong privacy controls and data protection measures
- [ ] **Emergency Contacts**: Verify availability of support and emergency contact information

### 🔍 Admin Oversight Requirements
- [ ] **Comprehensive Monitoring**: Check admin ability to monitor all user interactions
- [ ] **Content Approval**: Verify admin approval workflows for user-generated content
- [ ] **User Management**: Check robust user management and moderation capabilities
- [ ] **Activity Tracking**: Verify comprehensive tracking of user activities and behaviors
- [ ] **Intervention Capabilities**: Check admin ability to intervene in problematic situations

## Review Process

### 1. Pre-Review Setup
- [ ] Set up local development environment with test database
- [ ] Run all tests and ensure they pass
- [ ] Check that the application builds without errors
- [ ] Verify all dependencies are up to date and secure

### 2. Code Review Execution
- [ ] Review code changes line by line against this checklist
- [ ] Test functionality manually in browser
- [ ] Run accessibility audits using automated tools
- [ ] Verify database schema changes and migrations
- [ ] Check security implications of all changes

### 3. Testing & Validation
- [ ] Run comprehensive test suite including unit, integration, and E2E tests
- [ ] Perform manual accessibility testing with screen readers
- [ ] Test with keyboard navigation only
- [ ] Verify theme switching and color contrast compliance
- [ ] Test admin functionality and audit logging

### 4. Documentation Review
- [ ] Ensure all new features are properly documented
- [ ] Update API documentation for any endpoint changes
- [ ] Verify README and setup instructions are current
- [ ] Check that accessibility features are documented
- [ ] Update deployment and configuration documentation

## Review Outcome

### Approval Criteria
- [ ] All security requirements met with no vulnerabilities
- [ ] WCAG 2.1 AA compliance verified through testing
- [ ] All tests passing with adequate coverage
- [ ] Performance benchmarks met
- [ ] Code quality standards maintained
- [ ] Documentation complete and accurate

### Common Issues to Flag
- **Security**: Unvalidated inputs, missing authentication, weak session management
- **Accessibility**: Missing ARIA labels, poor color contrast, broken keyboard navigation
- **Performance**: Inefficient queries, large bundle sizes, missing optimizations
- **Code Quality**: Inconsistent patterns, missing error handling, poor documentation
- **AI Safety**: Inadequate content review, missing audit trails, weak rate limiting

## Tools & Resources

### Automated Testing Tools
- **Accessibility**: axe-core, WAVE, Lighthouse accessibility audit
- **Performance**: Lighthouse performance audit, WebPageTest
- **Security**: npm audit, Snyk, OWASP ZAP
- **Code Quality**: ESLint, Prettier, TypeScript compiler
- **Testing**: Vitest, Playwright, Testing Library

### Manual Testing Guidelines
- **Screen Readers**: Test with NVDA, JAWS, or VoiceOver
- **Keyboard Navigation**: Test all functionality without mouse
- **Mobile Testing**: Test on actual mobile devices and screen sizes
- **Theme Testing**: Verify all themes and high contrast modes
- **Admin Testing**: Test all admin workflows and audit logging

---

*This code review prompt is specifically designed for the FWFC website's unique requirements serving users with intellectual disabilities. Ensure all safety features, accessibility compliance, and admin oversight capabilities are thoroughly reviewed and tested.*