<script lang="ts">
  import { onMount } from 'svelte';
  import LoadingSpinner from './LoadingSpinner.svelte';
  
  // Types
  interface ErrorInfo {
    componentStack?: string;
    message: string;
    stack?: string;
    timestamp: string;
  }
  
  // Props
  export let fallback: any = null;
  export let title = 'Something went wrong';
  export let message = 'An unexpected error occurred. Please try again.';
  export let showDetails = false;
  export let onRetry: (() => void | Promise<void>) | undefined = undefined;
  export let onReport: ((error: Error, errorInfo: ErrorInfo) => void) | undefined = undefined;
  export let retryLabel = 'Try Again';
  export let reportLabel = 'Report Issue';
  
  // State
  let error: Error | null = null;
  let errorInfo: ErrorInfo | null = null;
  let isRetrying = false;
  let showErrorDetails = false;
  let errorElement: HTMLElement;
  
  // Error handler
  function handleError(event: ErrorEvent) {
    const errorObj = event.error || new Error(event.message || 'Unknown error occurred');
    error = errorObj;
    errorInfo = {
      componentStack: '',
      message: errorObj.message,
      stack: errorObj.stack,
      timestamp: new Date().toISOString()
    };
    
    // Log the error to console in development
    if (import.meta.env.DEV) {
      console.error('Error caught by boundary:', errorInfo);
    }
    
    // Focus the error element for accessibility
    setTimeout(() => {
      if (errorElement) {
        errorElement.focus();
      }
    }, 100);
    
    // Prevent the error from propagating
    event.preventDefault();
  }
  
  // Retry handler
  async function handleRetry() {
    if (!onRetry || isRetrying) return;
    
    isRetrying = true;
    try {
      await onRetry();
      // Clear error state on successful retry
      error = null;
      errorInfo = null;
      showErrorDetails = false;
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      // Update error with retry failure
      if (retryError instanceof Error) {
        error = retryError;
        errorInfo = {
          componentStack: '',
          message: retryError.message,
          stack: retryError.stack,
          timestamp: new Date().toISOString()
        };
      }
    } finally {
      isRetrying = false;
    }
  }
  
  // Report handler
  function handleReport() {
    if (onReport && error && errorInfo) {
      onReport(error, errorInfo);
    }
  }
  
  // Toggle error details
  function toggleDetails() {
    showErrorDetails = !showErrorDetails;
  }
  
  // Keyboard handler
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape' && showErrorDetails) {
      showErrorDetails = false;
    }
  }
  
  // Set up error handling
  onMount(() => {
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', (event) => {
      handleError(new ErrorEvent('unhandledrejection', {
        error: event.reason,
        message: event.reason?.message || 'Unhandled promise rejection'
      }));
    });
    
    return () => {
      window.removeEventListener('error', handleError);
    };
  });
</script>

{#if error}
  {#if fallback}
    <svelte:component this={fallback} {error} {errorInfo} />
  {:else}
    <div 
      class="error-boundary"
      bind:this={errorElement}
      tabindex="-1"
      role="alert"
      aria-live="assertive"
      on:keydown={handleKeyDown}
    >
      <div class="error-content">
        <div class="error-icon" aria-hidden="true">⚠️</div>
        <div class="error-text">
          <h2 class="error-title">{title}</h2>
          <p class="error-description">{message}</p>
          
          {#if showDetails && (error.stack || errorInfo?.stack)}
            <button
              class="details-toggle"
              on:click={toggleDetails}
              aria-expanded={showErrorDetails}
              aria-controls="error-details"
            >
              {showErrorDetails ? 'Hide' : 'Show'} Technical Details
            </button>
            
            {#if showErrorDetails}
              <div id="error-details" class="error-details">
                <h3>Error Information</h3>
                <div class="error-info">
                  <strong>Message:</strong> {error.message}
                </div>
                {#if errorInfo?.timestamp}
                  <div class="error-info">
                    <strong>Time:</strong> {new Date(errorInfo.timestamp).toLocaleString()}
                  </div>
                {/if}
                {#if error.stack}
                  <details class="stack-trace">
                    <summary>Stack Trace</summary>
                    <pre>{error.stack}</pre>
                  </details>
                {/if}
              </div>
            {/if}
          {/if}
        </div>
      </div>
      
      <div class="error-actions">
        {#if onRetry}
          <button
            class="retry-button"
            on:click={handleRetry}
            disabled={isRetrying}
            aria-describedby="retry-description"
          >
            {#if isRetrying}
              <LoadingSpinner size="small" inline message="" />
              Retrying...
            {:else}
              {retryLabel}
            {/if}
          </button>
          <div id="retry-description" class="sr-only">
            Attempt to recover from the error by retrying the failed operation
          </div>
        {/if}
        
        {#if onReport}
          <button
            class="report-button"
            on:click={handleReport}
            aria-describedby="report-description"
          >
            {reportLabel}
          </button>
          <div id="report-description" class="sr-only">
            Report this error to help improve the application
          </div>
        {/if}
        
        <button
          class="reload-button"
          on:click={() => window.location.reload()}
          aria-describedby="reload-description"
        >
          Reload Page
        </button>
        <div id="reload-description" class="sr-only">
          Reload the entire page to recover from the error
        </div>
      </div>
    </div>
  {/if}
{:else}
  <slot />
{/if}

<style>
  .error-boundary {
    background: var(--color-surface-error, #fef2f2);
    border: 2px solid var(--color-border-error, #fecaca);
    border-radius: var(--border-radius-lg, 8px);
    padding: 1.5rem;
    margin: 1rem 0;
    max-width: 100%;
    box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  }

  .error-boundary:focus {
    outline: 3px solid var(--color-interactive-primary);
    outline-offset: 2px;
  }

  .error-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .error-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 0.25rem;
  }

  .error-text {
    flex: 1;
    min-width: 0;
  }

  .error-title {
    color: var(--color-text-error, #dc2626);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
  }

  .error-description {
    color: var(--color-text-secondary, #6b7280);
    margin: 0 0 1rem 0;
    line-height: 1.6;
  }

  .details-toggle {
    background: transparent;
    border: 1px solid var(--color-border-primary, #d1d5db);
    color: var(--color-text-primary, #111827);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-md, 6px);
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
  }

  .details-toggle:hover,
  .details-toggle:focus {
    background: var(--color-surface-secondary, #f9fafb);
    border-color: var(--color-interactive-primary, #3b82f6);
    outline: 2px solid var(--color-interactive-primary, #3b82f6);
    outline-offset: 1px;
  }

  .error-details {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--color-border-secondary, #e5e7eb);
    border-radius: var(--border-radius-md, 6px);
    padding: 1rem;
    margin-top: 1rem;
  }

  .error-details h3 {
    color: var(--color-text-primary, #111827);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.75rem 0;
  }

  .error-info {
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .error-info strong {
    color: var(--color-text-primary, #111827);
    font-weight: 600;
  }

  .stack-trace {
    margin-top: 1rem;
  }

  .stack-trace summary {
    cursor: pointer;
    color: var(--color-text-secondary, #6b7280);
    font-weight: 600;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--color-border-secondary, #e5e7eb);
  }

  .stack-trace summary:hover,
  .stack-trace summary:focus {
    color: var(--color-interactive-primary, #3b82f6);
    outline: 2px solid var(--color-interactive-primary, #3b82f6);
    outline-offset: 1px;
  }

  .stack-trace pre {
    background: var(--color-surface-tertiary, #f3f4f6);
    border: 1px solid var(--color-border-secondary, #e5e7eb);
    border-radius: var(--border-radius-sm, 4px);
    padding: 1rem;
    margin: 0.5rem 0 0 0;
    overflow: auto;
    font-size: 0.75rem;
    line-height: 1.4;
    color: var(--color-text-secondary, #6b7280);
    max-height: 200px;
  }

  .error-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
  }

  .error-actions button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-md, 6px);
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }

  .retry-button {
    background: var(--color-interactive-primary, #3b82f6);
    color: white;
  }

  .retry-button:hover:not(:disabled),
  .retry-button:focus:not(:disabled) {
    background: var(--color-interactive-primary-hover, #2563eb);
    outline: 2px solid var(--color-interactive-primary, #3b82f6);
    outline-offset: 2px;
  }

  .retry-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .report-button {
    background: var(--color-surface-secondary, #f9fafb);
    color: var(--color-text-primary, #111827);
    border-color: var(--color-border-primary, #d1d5db);
  }

  .report-button:hover,
  .report-button:focus {
    background: var(--color-surface-tertiary, #f3f4f6);
    border-color: var(--color-interactive-primary, #3b82f6);
    outline: 2px solid var(--color-interactive-primary, #3b82f6);
    outline-offset: 1px;
  }

  .reload-button {
    background: var(--color-surface-warning, #fef3c7);
    color: var(--color-text-warning, #92400e);
    border-color: var(--color-border-warning, #fcd34d);
  }

  .reload-button:hover,
  .reload-button:focus {
    background: var(--color-surface-warning-hover, #fde68a);
    border-color: var(--color-border-warning-hover, #f59e0b);
    outline: 2px solid var(--color-border-warning, #fcd34d);
    outline-offset: 1px;
  }

  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .error-boundary {
      border-width: 3px;
    }
    
    .error-actions button {
      border-width: 2px;
      font-weight: 600;
    }
    
    .error-actions button:focus {
      outline-width: 3px;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .error-actions button,
    .details-toggle {
      transition: none;
    }
  }

  /* Mobile responsive */
  @media (max-width: 640px) {
    .error-boundary {
      padding: 1rem;
      margin: 0.5rem 0;
    }
    
    .error-content {
      gap: 0.75rem;
      margin-bottom: 1rem;
    }
    
    .error-actions {
      flex-direction: column;
      align-items: stretch;
    }
    
    .error-actions button {
      justify-content: center;
    }
  }
</style>
