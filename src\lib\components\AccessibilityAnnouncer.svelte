<script lang="ts">
	import { onMount } from 'svelte';

	export let message = '';
	export let priority: 'polite' | 'assertive' = 'polite';
	export let clearAfter = 5000; // Clear message after 5 seconds

	let announceElement: HTMLElement;
	let timeoutId: number;

	// Function to announce a message
	export function announce(text: string, urgency: 'polite' | 'assertive' = 'polite') {
		if (!announceElement) return;

		// Clear any existing timeout
		if (timeoutId) {
			clearTimeout(timeoutId);
		}

		// Set the priority
		priority = urgency;
		
		// Clear the element first to ensure screen readers notice the change
		announceElement.textContent = '';
		
		// Use a small delay to ensure the clear is processed
		setTimeout(() => {
			announceElement.textContent = text;
			
			// Clear the message after specified time
			if (clearAfter > 0) {
				timeoutId = setTimeout(() => {
					if (announceElement) {
						announceElement.textContent = '';
					}
				}, clearAfter);
			}
		}, 100);
	}

	// React to prop changes
	$: if (message && announceElement) {
		announce(message, priority);
	}

	onMount(() => {
		// Expose announce function globally for easy access
		if (typeof window !== 'undefined') {
			window.announceToScreenReader = announce;
		}

		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId);
			}
		};
	});
</script>

<!-- Screen reader only announcement area -->
<div
	bind:this={announceElement}
	class="sr-only"
	aria-live={priority}
	aria-atomic="true"
	role="status"
></div>

<style>
	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}
</style>