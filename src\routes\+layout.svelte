<script lang="ts">
	import Header from './Header.svelte';
	import '../app.css';
	import '../lib/styles/theme-tokens.css';
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import ThemeProvider from '$lib/components/ThemeProvider.svelte';
	import SkipLinks from '$lib/components/SkipLinks.svelte';
	import AccessibilityAnnouncer from '$lib/components/AccessibilityAnnouncer.svelte';
	import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';

	let { children } = $props();
	let highContrast = $state(false);
	let largeText = $state(false);
	let simplifiedInterface = $state(false);
	let showDebugPanel = $state(false);
	let announcements = $state('');
	let skipLinkVisible = $state(false);

	function handleKeyDown(event: KeyboardEvent) {
		if (event.ctrlKey && event.shiftKey && event.key === 'D') {
			event.preventDefault();
			showDebugPanel = !showDebugPanel;
		}

		// Show skip links on Tab key
		if (event.key === 'Tab' && !event.shiftKey) {
			skipLinkVisible = true;
		}
	}

	// Skip to main content
	function skipToMain() {
		const mainContent = document.getElementById('main-content');
		if (mainContent) {
			mainContent.focus();
			announcements = 'Skipped to main content';
		}
	}

	// Skip to navigation
	function skipToNav() {
		const navigation = document.getElementById('main-navigation');
		if (navigation) {
			navigation.focus();
			announcements = 'Skipped to navigation';
		}
	}

	// Handle accessibility control changes with announcements
	function toggleHighContrast() {
		highContrast = !highContrast;
		announcements = `High contrast ${highContrast ? 'enabled' : 'disabled'}`;
	}

	function toggleLargeText() {
		largeText = !largeText;
		announcements = `Large text ${largeText ? 'enabled' : 'disabled'}`;
	}

	function toggleSimplifiedInterface() {
		simplifiedInterface = !simplifiedInterface;
		announcements = `Simplified interface ${simplifiedInterface ? 'enabled' : 'disabled'}`;
	}

	$effect(() => {
		if ($page.data.accessibility) {
			highContrast = $page.data.accessibility.highContrast;
			largeText = $page.data.accessibility.largeText;
			simplifiedInterface = $page.data.accessibility.simplifiedInterface;
		}
	});

	$effect(() => {
		if (typeof document !== 'undefined') {
			if (highContrast) {
				document.body.classList.add('high-contrast');
			} else {
				document.body.classList.remove('high-contrast');
			}

			if (largeText) {
				document.body.classList.add('large-text');
			} else {
				document.body.classList.remove('large-text');
			}

			if (simplifiedInterface) {
				document.body.classList.add('simplified-interface');
			} else {
				document.body.classList.remove('simplified-interface');
			}
		}
	});

	// Clear announcements after they're read
	$effect(() => {
		if (announcements) {
			const timer = setTimeout(() => {
				announcements = '';
			}, 1000);
			return () => clearTimeout(timer);
		}
	});

	// Hide skip links when focus moves away
	$effect(() => {
		if (skipLinkVisible) {
			const timer = setTimeout(() => {
				const activeElement = document.activeElement;
				if (!activeElement?.classList.contains('skip-link')) {
					skipLinkVisible = false;
				}
			}, 100);
			return () => clearTimeout(timer);
		}
	});

	onMount(() => {
		window.addEventListener('keydown', handleKeyDown);

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	});
</script>

<svelte:head>
	<title>Finn Wolfhard Fan Club</title>
	<meta name="description" content="Official fan club for Finn Wolfhard" />
</svelte:head>

<ThemeProvider>
	<!-- Enhanced Skip Links Component -->
	<SkipLinks links={[
		{ href: '#main-content', label: 'Skip to main content' },
		{ href: '#main-navigation', label: 'Skip to navigation' },
		{ href: '#accessibility-controls', label: 'Skip to accessibility controls' }
	]} />

	<!-- Enhanced Accessibility Announcer -->
	<AccessibilityAnnouncer message={announcements} priority="polite" />

	<div class="app" class:high-contrast={highContrast} class:large-text={largeText} class:simplified-interface={simplifiedInterface}>
		<Header />

		<main id="main-content" tabindex="-1" aria-label="Main content">
			<ErrorBoundary
				title="Application Error"
				message="Something went wrong while loading this page. Please try refreshing or contact support if the problem persists."
				showDetails={true}
				onRetry={() => window.location.reload()}
			>
				{@render children()}
			</ErrorBoundary>
		</main>

	<footer>
		<div id="accessibility-controls" class="accessibility-controls" role="group" aria-label="Accessibility settings">
			<button
				onclick={toggleHighContrast}
				class:active={highContrast}
				aria-pressed={highContrast}
				aria-describedby="high-contrast-desc"
			>
				High Contrast
			</button>
			<span id="high-contrast-desc" class="sr-only">Toggle high contrast mode for better visibility</span>

			<button
				onclick={toggleLargeText}
				class:active={largeText}
				aria-pressed={largeText}
				aria-describedby="large-text-desc"
			>
				Large Text
			</button>
			<span id="large-text-desc" class="sr-only">Increase text size for better readability</span>

			<button
				onclick={toggleSimplifiedInterface}
				class:active={simplifiedInterface}
				aria-pressed={simplifiedInterface}
				aria-describedby="simplified-desc"
			>
				Simplified Interface
			</button>
			<span id="simplified-desc" class="sr-only">Hide complex UI elements for easier navigation</span>
		</div>
		<p>
			&copy; 2025 Finn Wolfhard Fan Club | <a href="/about">About</a> | <a href="/privacy">Privacy</a>
		</p>
	</footer>

	{#if showDebugPanel}
		<div class="debug-panel">
			<h3>Debug Panel</h3>
			<p>Press Ctrl+Shift+D to close</p>
			<div class="debug-info">
				<p>High Contrast: {highContrast ? 'On' : 'Off'}</p>
				<p>Large Text: {largeText ? 'On' : 'Off'}</p>
				<p>Simplified Interface: {simplifiedInterface ? 'On' : 'Off'}</p>
				<p>Current Route: {$page.url.pathname}</p>
				<p>User: {$page.data.user ? $page.data.user.displayName : 'Not logged in'}</p>
			</div>
		</div>
	{/if}
</div>
</ThemeProvider>

<style>
	:global(body) {
		background-color: var(--color-bg-primary);
		color: var(--color-text-primary);
		margin: 0;
		padding: 0;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		line-height: var(--line-height-normal);
		transition: var(--transition-theme);
	}

	.app {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: var(--color-bg-primary);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
	}

	main {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: var(--space-md);
		width: 100%;
		max-width: 64rem;
		margin: 0 auto;
		box-sizing: border-box;
		background-color: var(--color-bg-primary);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
	}

	footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: var(--space-md);
		background-color: var(--color-surface-secondary);
		border-top: var(--border-width-thin) solid var(--color-border-primary);
		color: var(--color-text-secondary);
		transition: var(--transition-theme);
	}

	footer a {
		font-weight: var(--font-weight-semibold);
		color: var(--color-interactive-primary);
		text-decoration: none;
		transition: var(--transition-theme);
	}

	footer a:hover {
		color: var(--color-interactive-primary-hover);
		text-decoration: underline;
	}

	.accessibility-controls {
		display: flex;
		gap: var(--space-sm);
		margin-bottom: var(--space-sm);
		flex-wrap: wrap;
		justify-content: center;
	}

	.accessibility-controls button {
		background-color: var(--color-button-secondary-bg);
		border: var(--border-width-thin) solid var(--color-button-secondary-border);
		color: var(--color-button-secondary-text);
		padding: var(--space-xs) var(--space-sm);
		border-radius: var(--border-radius-md);
		cursor: pointer;
		font-size: var(--font-size-sm);
		font-weight: var(--font-weight-medium);
		transition: var(--transition-theme);
	}

	.accessibility-controls button:hover {
		background-color: var(--color-button-secondary-hover-bg);
		color: var(--color-button-secondary-hover-text);
		border-color: var(--color-border-focus);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	.accessibility-controls button:focus-visible {
		box-shadow: 0 0 0 3px var(--color-shadow-focus);
		border-color: var(--color-border-focus);
	}

	.accessibility-controls button.active {
		background-color: var(--color-interactive-primary);
		color: var(--color-text-inverse);
		border-color: var(--color-interactive-primary);
	}

	.debug-panel {
		position: fixed;
		bottom: 20px;
		right: 20px;
		background-color: rgba(0, 0, 0, 0.8);
		color: white;
		padding: 15px;
		border-radius: 5px;
		z-index: 1000;
		max-width: 300px;
	}

	.debug-panel h3 {
		margin-top: 0;
	}

	.debug-info {
		font-family: monospace;
		font-size: 0.9rem;
	}

	/* Skip Links for Accessibility */
	.skip-links {
		position: fixed;
		top: -100px;
		left: 0;
		z-index: 9999;
		background: var(--theme-bg-primary);
		border: 2px solid var(--theme-accent-primary);
		border-radius: 0 0 4px 0;
		padding: 0.5rem;
		transition: top 0.3s ease;
	}

	.skip-links.visible {
		top: 0;
	}

	.skip-link {
		display: block;
		background: var(--theme-accent-primary);
		color: white;
		padding: 0.5rem 1rem;
		margin: 0.25rem 0;
		border: none;
		border-radius: 4px;
		text-decoration: none;
		font-weight: bold;
		cursor: pointer;
		transition: all 0.2s ease;
	}

	.skip-link:hover,
	.skip-link:focus {
		background: var(--theme-accent-primary-hover);
		outline: 2px solid var(--theme-accent-primary);
		outline-offset: 2px;
	}

	/* Screen Reader Only Content */
	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}

	/* Focus Management */
	:global(*:focus) {
		outline: 2px solid var(--color-border-focus);
		outline-offset: 2px;
	}

	:global(button:focus),
	:global(input:focus),
	:global(select:focus),
	:global(textarea:focus),
	:global(a:focus) {
		outline: 2px solid var(--color-border-focus);
		outline-offset: 2px;
		box-shadow: 0 0 0 4px var(--color-shadow-focus);
	}

	/* High Contrast Mode Support */
	@media (prefers-contrast: high) {
		:global(*) {
			--color-text-secondary: var(--color-text-primary);
			--color-text-muted: var(--color-text-primary);
		}
	}

	/* Reduced Motion Support */
	@media (prefers-reduced-motion: reduce) {
		:global(*),
		:global(*::before),
		:global(*::after) {
			animation-duration: 0.01ms !important;
			animation-iteration-count: 1 !important;
			transition-duration: 0.01ms !important;
		}
	}

	/* Accessibility styles */
	:global(.high-contrast) {
		--color-bg-0: #000000;
		--color-bg-1: #000000;
		--color-bg-2: #000000;
		--color-text: #ffffff;
		--color-theme-1: #ffff00;
		--color-theme-2: #00ffff;
	}

	:global(.high-contrast) .app {
		background-color: #000000;
		color: #ffffff;
	}

	:global(.high-contrast) a {
		color: #ffff00;
	}

	:global(.high-contrast) .app {
		--color-bg-primary: #000000;
		--color-bg-secondary: #1a1a1a;
		--color-bg-tertiary: #333333;
		--color-surface-primary: #000000;
		--color-surface-secondary: #1a1a1a;
		--color-text-primary: #ffffff;
		--color-text-secondary: #cccccc;
		--color-text-muted: #999999;
		--color-border-primary: #666666;
		--color-border-secondary: #888888;
		--color-interactive-primary: #ffff00;
		--color-interactive-primary-hover: #ffff66;
		--color-button-secondary-text: #ffff00;
		--color-button-secondary-border: #ffff00;
	}

	:global(.large-text) {
		font-size: 1.2rem;
	}

	:global(.large-text) * {
		font-size: 1.2em !important;
	}

	@media (min-width: 480px) {
		footer {
			padding: 12px 0;
		}
	}
</style>
