import DOMPurify from 'isomorphic-dompurify';
import { z } from 'zod';

/**
 * Enhanced input validation and sanitization service
 * Implements security best practices for FWFC application
 */

// Common validation schemas
export const ValidationSchemas = {
  // User input schemas
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be no more than 20 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),

  displayName: z.string()
    .min(2, 'Display name must be at least 2 characters')
    .max(50, 'Display name must be no more than 50 characters')
    .regex(/^[a-zA-Z0-9\s_-]+$/, 'Display name contains invalid characters'),

  email: z.string()
    .email('Invalid email address')
    .max(254, 'Email address is too long'),

  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password is too long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  // Content schemas
  title: z.string()
    .min(1, 'Title is required')
    .max(200, 'Title is too long')
    .trim(),

  content: z.string()
    .min(1, 'Content is required')
    .max(50000, 'Content is too long'),

  description: z.string()
    .max(1000, 'Description is too long')
    .optional(),

  // URL validation
  url: z.string()
    .url('Invalid URL')
    .max(2048, 'URL is too long')
    .optional(),

  // File validation
  filename: z.string()
    .min(1, 'Filename is required')
    .max(255, 'Filename is too long')
    .regex(/^[a-zA-Z0-9._-]+$/, 'Filename contains invalid characters'),

  // ID validation
  id: z.number()
    .int('ID must be an integer')
    .positive('ID must be positive'),

  // Role validation
  role: z.enum(['admin', 'moderator', 'user'], {
    errorMap: () => ({ message: 'Invalid role' })
  }),

  // Status validation
  userStatus: z.enum(['active', 'inactive', 'suspended'], {
    errorMap: () => ({ message: 'Invalid user status' })
  }),

  // AI content validation
  aiPrompt: z.string()
    .min(10, 'AI prompt must be at least 10 characters')
    .max(2000, 'AI prompt is too long'),

  authenticityScore: z.number()
    .int('Authenticity score must be an integer')
    .min(0, 'Authenticity score must be at least 0')
    .max(100, 'Authenticity score must be at most 100'),

  // Message board validation
  boardSlug: z.string()
    .min(1, 'Board slug is required')
    .max(50, 'Board slug is too long')
    .regex(/^[a-z0-9-]+$/, 'Board slug can only contain lowercase letters, numbers, and hyphens'),

  topicSlug: z.string()
    .min(1, 'Topic slug is required')
    .max(100, 'Topic slug is too long')
    .regex(/^[a-z0-9-]+$/, 'Topic slug can only contain lowercase letters, numbers, and hyphens'),
};

/**
 * Sanitize HTML content to prevent XSS attacks
 */
export function sanitizeHtml(html: string, options?: {
  allowedTags?: string[];
  allowedAttributes?: Record<string, string[]>;
  stripTags?: boolean;
}): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  const defaultConfig = {
    ALLOWED_TAGS: options?.allowedTags || [
      'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'blockquote', 'a', 'img', 'code', 'pre'
    ],
    ALLOWED_ATTR: options?.allowedAttributes || {
      'a': ['href', 'title', 'target', 'rel'],
      'img': ['src', 'alt', 'title', 'width', 'height'],
      '*': ['class']
    },
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_TRUSTED_TYPE: false
  };

  if (options?.stripTags) {
    return DOMPurify.sanitize(html, { ALLOWED_TAGS: [], KEEP_CONTENT: true });
  }

  return DOMPurify.sanitize(html, defaultConfig);
}

/**
 * Sanitize plain text input
 */
export function sanitizeText(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  return text
    .trim()
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
    .replace(/\s+/g, ' '); // Normalize whitespace
}

/**
 * Validate and sanitize user input
 */
export function validateAndSanitize<T>(
  data: unknown,
  schema: z.ZodSchema<T>,
  sanitizeOptions?: {
    sanitizeHtml?: boolean;
    sanitizeText?: boolean;
    htmlFields?: string[];
    textFields?: string[];
  }
): { success: true; data: T } | { success: false; errors: string[] } {
  try {
    // First validate the structure
    const validatedData = schema.parse(data);

    // Then sanitize if requested
    if (sanitizeOptions && typeof validatedData === 'object' && validatedData !== null) {
      const sanitized = { ...validatedData } as any;

      // Sanitize HTML fields
      if (sanitizeOptions.sanitizeHtml && sanitizeOptions.htmlFields) {
        for (const field of sanitizeOptions.htmlFields) {
          if (sanitized[field] && typeof sanitized[field] === 'string') {
            sanitized[field] = sanitizeHtml(sanitized[field]);
          }
        }
      }

      // Sanitize text fields
      if (sanitizeOptions.sanitizeText && sanitizeOptions.textFields) {
        for (const field of sanitizeOptions.textFields) {
          if (sanitized[field] && typeof sanitized[field] === 'string') {
            sanitized[field] = sanitizeText(sanitized[field]);
          }
        }
      }

      return { success: true, data: sanitized };
    }

    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      );
      return { success: false, errors };
    }

    return { success: false, errors: ['Validation failed'] };
  }
}

/**
 * Validate file uploads
 */
export function validateFileUpload(file: {
  name: string;
  size: number;
  type: string;
}, options: {
  maxSize?: number;
  allowedTypes?: string[];
  allowedExtensions?: string[];
}): { valid: true } | { valid: false; error: string } {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
  } = options;

  // Check file size
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
    };
  }

  // Check MIME type
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed`
    };
  }

  // Check file extension
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  if (!allowedExtensions.includes(extension)) {
    return {
      valid: false,
      error: `File extension ${extension} is not allowed`
    };
  }

  // Check filename for security
  const filenameValidation = ValidationSchemas.filename.safeParse(file.name);
  if (!filenameValidation.success) {
    return {
      valid: false,
      error: 'Invalid filename'
    };
  }

  return { valid: true };
}

/**
 * Check for suspicious patterns in user input
 */
export function detectSuspiciousContent(content: string): {
  suspicious: boolean;
  reasons: string[];
  riskLevel: 'low' | 'medium' | 'high';
} {
  const reasons: string[] = [];
  let riskLevel: 'low' | 'medium' | 'high' = 'low';

  // Check for common XSS patterns
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe[^>]*>/gi,
    /<object[^>]*>/gi,
    /<embed[^>]*>/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi
  ];

  for (const pattern of xssPatterns) {
    if (pattern.test(content)) {
      reasons.push('Potential XSS content detected');
      riskLevel = 'high';
      break;
    }
  }

  // Check for SQL injection patterns
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /('|\"|;|--|\*|\/\*|\*\/)/g
  ];

  for (const pattern of sqlPatterns) {
    if (pattern.test(content)) {
      reasons.push('Potential SQL injection detected');
      if (riskLevel !== 'high') riskLevel = 'medium';
      break;
    }
  }

  // Check for excessive special characters
  const specialCharCount = (content.match(/[<>{}[\]\\|`~!@#$%^&*()+=]/g) || []).length;
  const specialCharRatio = specialCharCount / content.length;
  
  if (specialCharRatio > 0.3) {
    reasons.push('High ratio of special characters');
    if (riskLevel === 'low') riskLevel = 'medium';
  }

  // Check for very long lines (potential buffer overflow attempts)
  const lines = content.split('\n');
  const maxLineLength = Math.max(...lines.map(line => line.length));
  
  if (maxLineLength > 1000) {
    reasons.push('Extremely long line detected');
    if (riskLevel === 'low') riskLevel = 'medium';
  }

  // Check for base64 encoded content (potential payload)
  const base64Pattern = /^[A-Za-z0-9+/]{20,}={0,2}$/;
  if (base64Pattern.test(content.replace(/\s/g, ''))) {
    reasons.push('Potential base64 encoded content');
    if (riskLevel === 'low') riskLevel = 'medium';
  }

  return {
    suspicious: reasons.length > 0,
    reasons,
    riskLevel
  };
}

/**
 * Rate limiting validation for user actions
 */
export function validateActionFrequency(
  userId: number,
  action: string,
  timeWindow: number = 60000, // 1 minute default
  maxActions: number = 10
): { allowed: boolean; resetTime?: number } {
  // This would typically use a more sophisticated rate limiting system
  // For now, we'll use a simple in-memory approach
  const key = `${userId}:${action}`;
  const now = Date.now();
  
  // In a real implementation, this would use Redis or similar
  // For demo purposes, we'll use a simple Map
  if (!global.actionTracker) {
    global.actionTracker = new Map();
  }
  
  const tracker = global.actionTracker;
  const userActions = tracker.get(key) || [];
  
  // Remove old actions outside the time window
  const recentActions = userActions.filter((timestamp: number) => 
    now - timestamp < timeWindow
  );
  
  if (recentActions.length >= maxActions) {
    const oldestAction = Math.min(...recentActions);
    const resetTime = oldestAction + timeWindow;
    
    return {
      allowed: false,
      resetTime
    };
  }
  
  // Record this action
  recentActions.push(now);
  tracker.set(key, recentActions);
  
  return { allowed: true };
}

/**
 * Comprehensive input validation for API endpoints
 */
export function createApiValidator<T>(schema: z.ZodSchema<T>) {
  return (data: unknown) => {
    const result = validateAndSanitize(data, schema, {
      sanitizeHtml: true,
      sanitizeText: true,
      htmlFields: ['content', 'description', 'bio'],
      textFields: ['title', 'username', 'displayName', 'email']
    });

    if (!result.success) {
      const error = new Error('Validation failed');
      (error as any).status = 400;
      (error as any).errors = result.errors;
      throw error;
    }

    // Check for suspicious content
    const contentFields = ['content', 'description', 'bio', 'title'];
    for (const field of contentFields) {
      const value = (result.data as any)[field];
      if (value && typeof value === 'string') {
        const suspiciousCheck = detectSuspiciousContent(value);
        if (suspiciousCheck.suspicious && suspiciousCheck.riskLevel === 'high') {
          const error = new Error('Content contains potentially malicious patterns');
          (error as any).status = 400;
          (error as any).reasons = suspiciousCheck.reasons;
          throw error;
        }
      }
    }

    return result.data;
  };
}

// Export commonly used validators
export const ApiValidators = {
  createUser: createApiValidator(z.object({
    username: ValidationSchemas.username,
    displayName: ValidationSchemas.displayName,
    email: ValidationSchemas.email,
    password: ValidationSchemas.password,
    bio: ValidationSchemas.description,
    role: ValidationSchemas.role.optional()
  })),

  updateUser: createApiValidator(z.object({
    displayName: ValidationSchemas.displayName.optional(),
    email: ValidationSchemas.email.optional(),
    bio: ValidationSchemas.description.optional(),
    status: ValidationSchemas.userStatus.optional()
  })),

  createContent: createApiValidator(z.object({
    title: ValidationSchemas.title,
    content: ValidationSchemas.content,
    description: ValidationSchemas.description,
    published: z.boolean().optional()
  })),

  createTopic: createApiValidator(z.object({
    title: ValidationSchemas.title,
    content: ValidationSchemas.content,
    boardId: ValidationSchemas.id
  })),

  createPost: createApiValidator(z.object({
    content: ValidationSchemas.content,
    topicId: ValidationSchemas.id,
    parentPostId: ValidationSchemas.id.optional()
  }))
};

// Declare global type for action tracker
declare global {
  var actionTracker: Map<string, number[]> | undefined;
}