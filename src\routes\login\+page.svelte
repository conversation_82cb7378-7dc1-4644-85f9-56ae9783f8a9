<script>
	import { goto } from '$app/navigation';
	import { onMount } from 'svelte';

	// Form state
	let email = '';
	let password = '';
	let rememberMe = false;
	let errorMessage = '';
	let isLoading = false;
	let registrationSuccess = false;

	// Check for registration success parameter
	onMount(() => {
		const urlParams = new URLSearchParams(window.location.search);
		registrationSuccess = urlParams.get('registered') === 'true';
	});

	// Handle form submission
	async function handleSubmit() {
		// Reset error message
		errorMessage = '';

		// Validate form
		if (!email || !password) {
			errorMessage = 'Please enter both email and password.';
			return;
		}

		// Show loading state
		isLoading = true;

		try {
			// Send login request to the API
			const response = await fetch('/api/auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					email,
					password
				})
			});

			const data = await response.json();

			if (response.ok && data.success) {
				// Check if user is admin
				if (data.data.role === 'admin') {
					// Redirect to admin dashboard
					goto('/admin');
				} else {
					// Redirect to home page for regular users
					goto('/');
				}
			} else {
				// Show error message
				errorMessage = data.error || 'Invalid email or password. Please try again.';
				isLoading = false;
			}
		} catch (error) {
			console.error('Login error:', error);
			errorMessage = 'An error occurred during login. Please try again.';
			isLoading = false;
		}
	}
</script>

<svelte:head>
	<title>Login - Finn Wolfhard Fan Club</title>
	<meta name="description" content="Log in to your Finn Wolfhard Fan Club account" />
</svelte:head>

<div class="login-container">
	<div class="login-card">
		<h1>Log In</h1>

		{#if registrationSuccess}
			<div class="success-message">
				<h2>🎉 Registration Successful!</h2>
				<p>Welcome to the Finn Wolfhard Fan Club! You can now log in with your new account.</p>
			</div>
		{/if}

		{#if errorMessage}
			<div class="error-message">
				{errorMessage}
			</div>
		{/if}

		<form on:submit|preventDefault={handleSubmit}>
			<div class="form-group">
				<label for="email">Email</label>
				<input
					type="email"
					id="email"
					bind:value={email}
					placeholder="Enter your email"
					disabled={isLoading}
				/>
			</div>

			<div class="form-group">
				<label for="password">Password</label>
				<input
					type="password"
					id="password"
					bind:value={password}
					placeholder="Enter your password"
					disabled={isLoading}
				/>
			</div>

			<div class="form-options">
				<div class="remember-me">
					<input
						type="checkbox"
						id="remember-me"
						bind:checked={rememberMe}
						disabled={isLoading}
					/>
					<label for="remember-me">Remember me</label>
				</div>

				<a href="/forgot-password" class="forgot-password">Forgot password?</a>
			</div>

			<button type="submit" class="btn primary" disabled={isLoading}>
				{#if isLoading}
					Logging in...
				{:else}
					Log In
				{/if}
			</button>
		</form>

		<div class="register-prompt">
			<p>Don't have an account? <a href="/register">Register</a></p>
		</div>

		<div class="demo-credentials">
			<p>For admin access, use:</p>
			<p>Email: <strong><EMAIL></strong></p>
			<p>Password: <strong>admin</strong></p>
			<p class="demo-note">For regular user access:</p>
			<p>Email: <strong><EMAIL></strong></p>
			<p>Password: <strong>password</strong></p>
		</div>
	</div>

	<div class="accessibility-note">
		<h2>Accessibility Features</h2>
		<p>Our website is designed to be accessible to all users. You can customize your experience using the accessibility controls in the footer.</p>
		<ul>
			<li>High Contrast Mode: Enhances visibility with stronger color contrasts</li>
			<li>Large Text Mode: Increases text size throughout the site</li>
			<li>Simplified Interface: Reduces visual complexity for easier navigation</li>
		</ul>
		<p>Need assistance? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
	</div>
</div>

<style>
	.login-container {
		max-width: 1000px;
		margin: 0 auto;
		padding: var(--space-xl) var(--space-md);
		display: flex;
		flex-direction: column;
		gap: var(--space-xl);
	}

	.login-card {
		background-color: var(--color-surface-primary);
		border: var(--border-width-thin) solid var(--color-border-primary);
		border-radius: var(--border-radius-lg);
		box-shadow: var(--shadow-lg);
		padding: var(--space-xl);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
	}

	h1 {
		text-align: center;
		margin-bottom: var(--space-lg);
		color: var(--color-text-primary);
		font-size: var(--font-size-2xl);
		font-weight: var(--font-weight-bold);
	}

	.success-message {
		background: var(--color-success-bg);
		color: var(--color-success);
		padding: var(--space-lg);
		border-radius: var(--border-radius-lg);
		margin-bottom: var(--space-lg);
		text-align: center;
		border: var(--border-width-thin) solid var(--color-success-border);
		transition: var(--transition-theme);
	}

	.success-message h2 {
		margin: 0 0 var(--space-sm) 0;
		font-size: var(--font-size-xl);
		font-weight: var(--font-weight-semibold);
		color: var(--color-success);
	}

	.success-message p {
		margin: 0;
		font-size: var(--font-size-md);
		line-height: var(--line-height-normal);
		color: var(--color-success);
	}

	.error-message {
		background-color: var(--color-error-bg);
		color: var(--color-error);
		padding: var(--space-md);
		border-radius: var(--border-radius-md);
		margin-bottom: var(--space-lg);
		text-align: center;
		border: var(--border-width-thin) solid var(--color-error-border);
		transition: var(--transition-theme);
	}

	.form-group {
		margin-bottom: var(--space-lg);
	}

	label {
		display: block;
		margin-bottom: var(--space-sm);
		font-weight: var(--font-weight-medium);
		color: var(--color-text-primary);
		font-size: var(--font-size-sm);
	}

	input[type="email"],
	input[type="password"] {
		width: 100%;
		padding: var(--space-md);
		border: var(--border-width-thin) solid var(--color-input-border);
		border-radius: var(--border-radius-md);
		font-size: var(--font-size-md);
		background-color: var(--color-input-bg);
		color: var(--color-input-text);
		box-sizing: border-box;
		transition: var(--transition-theme);
	}

	input[type="email"]:focus,
	input[type="password"]:focus {
		outline: none;
		border-color: var(--color-input-border-focus);
		box-shadow: 0 0 0 3px var(--color-shadow-focus);
	}

	.form-options {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--space-lg);
	}

	.remember-me {
		display: flex;
		align-items: center;
		gap: var(--space-sm);
		font-size: var(--font-size-sm);
		color: var(--color-text-secondary);
	}

	.remember-me input {
		margin: 0;
	}

	.forgot-password {
		color: var(--color-interactive-primary);
		text-decoration: none;
		font-size: var(--font-size-sm);
		transition: var(--transition-theme);
	}

	.forgot-password:hover {
		color: var(--color-interactive-primary-hover);
		text-decoration: underline;
	}

	.btn {
		display: block;
		width: 100%;
		padding: var(--space-md);
		border: var(--border-width-thin) solid transparent;
		border-radius: var(--border-radius-md);
		font-weight: var(--font-weight-semibold);
		cursor: pointer;
		transition: var(--transition-theme);
		font-size: var(--font-size-md);
		font-family: inherit;
	}

	.btn.primary {
		background-color: var(--color-button-primary-bg);
		color: var(--color-button-primary-text);
		border-color: var(--color-button-primary-border);
	}

	.btn.primary:hover:not(:disabled) {
		background-color: var(--color-button-primary-hover-bg);
		border-color: var(--color-button-primary-hover-border);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.btn:focus-visible {
		box-shadow: 0 0 0 3px var(--color-shadow-focus);
	}

	.btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}

	.register-prompt {
		text-align: center;
		margin-top: var(--space-lg);
		padding-top: var(--space-lg);
		border-top: var(--border-width-thin) solid var(--color-border-primary);
		color: var(--color-text-primary);
		font-size: var(--font-size-sm);
	}

	.register-prompt a {
		color: var(--color-interactive-primary);
		font-weight: var(--font-weight-semibold);
		text-decoration: none;
		transition: var(--transition-theme);
	}

	.register-prompt a:hover {
		color: var(--color-interactive-primary-hover);
		text-decoration: underline;
	}

	.demo-credentials {
		margin-top: var(--space-lg);
		padding: var(--space-md);
		background-color: var(--color-info-bg);
		border: var(--border-width-thin) solid var(--color-info-border);
		border-radius: var(--border-radius-md);
		font-size: var(--font-size-sm);
		color: var(--color-info);
		transition: var(--transition-theme);
	}

	.demo-credentials p {
		margin: var(--space-xs) 0;
		line-height: var(--line-height-normal);
	}

	.demo-credentials strong {
		color: var(--color-info);
		font-weight: var(--font-weight-semibold);
	}

	.demo-note {
		margin-top: var(--space-md);
		font-weight: var(--font-weight-semibold);
		border-top: var(--border-width-thin) solid var(--color-border-secondary);
		padding-top: var(--space-md);
		color: var(--color-text-primary);
	}

	.accessibility-note {
		background-color: var(--color-surface-secondary);
		border: var(--border-width-thin) solid var(--color-border-primary);
		padding: var(--space-lg);
		border-radius: var(--border-radius-lg);
		color: var(--color-text-primary);
		transition: var(--transition-theme);
	}

	.accessibility-note h2 {
		margin-top: 0;
		margin-bottom: var(--space-md);
		font-size: var(--font-size-xl);
		font-weight: var(--font-weight-semibold);
		color: var(--color-text-primary);
	}

	.accessibility-note ul {
		margin-bottom: var(--space-md);
		padding-left: var(--space-lg);
	}

	.accessibility-note li {
		margin-bottom: var(--space-sm);
		color: var(--color-text-secondary);
		line-height: var(--line-height-normal);
	}

	.accessibility-note a {
		color: var(--color-interactive-primary);
		text-decoration: none;
		transition: var(--transition-theme);
	}

	.accessibility-note a:hover {
		color: var(--color-interactive-primary-hover);
		text-decoration: underline;
	}

	@media (min-width: 768px) {
		.login-container {
			flex-direction: row;
			align-items: flex-start;
			gap: var(--space-2xl);
		}

		.login-card {
			flex: 1;
		}

		.accessibility-note {
			flex: 1;
		}
	}
</style>
