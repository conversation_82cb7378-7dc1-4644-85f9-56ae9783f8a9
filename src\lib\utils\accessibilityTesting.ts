/**
 * Accessibility testing utilities for FWFC application
 * Provides automated accessibility checks and testing helpers
 */

export interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  rule: string;
  description: string;
  element?: Element;
  selector?: string;
  impact: 'critical' | 'serious' | 'moderate' | 'minor';
  wcagLevel: 'A' | 'AA' | 'AAA';
  wcagCriteria: string[];
}

export interface AccessibilityTestResult {
  passed: boolean;
  issues: AccessibilityIssue[];
  summary: {
    total: number;
    errors: number;
    warnings: number;
    info: number;
  };
  score: number; // 0-100 accessibility score
}

/**
 * Check color contrast ratios
 */
export function checkColorContrast(element: Element): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  
  if (typeof window === 'undefined') return issues;
  
  const computedStyle = window.getComputedStyle(element);
  const backgroundColor = computedStyle.backgroundColor;
  const color = computedStyle.color;
  const fontSize = parseFloat(computedStyle.fontSize);
  const fontWeight = computedStyle.fontWeight;
  
  // Skip if colors are not set or transparent
  if (!backgroundColor || backgroundColor === 'rgba(0, 0, 0, 0)' || 
      !color || color === 'rgba(0, 0, 0, 0)') {
    return issues;
  }
  
  const contrast = calculateContrastRatio(color, backgroundColor);
  const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
  
  const minContrast = isLargeText ? 3.0 : 4.5; // WCAG AA requirements
  const minContrastAAA = isLargeText ? 4.5 : 7.0; // WCAG AAA requirements
  
  if (contrast < minContrast) {
    issues.push({
      type: 'error',
      rule: 'color-contrast',
      description: `Insufficient color contrast ratio: ${contrast.toFixed(2)}:1 (minimum: ${minContrast}:1)`,
      element,
      selector: getElementSelector(element),
      impact: 'serious',
      wcagLevel: 'AA',
      wcagCriteria: ['1.4.3']
    });
  } else if (contrast < minContrastAAA) {
    issues.push({
      type: 'warning',
      rule: 'color-contrast-aaa',
      description: `Color contrast could be improved: ${contrast.toFixed(2)}:1 (AAA minimum: ${minContrastAAA}:1)`,
      element,
      selector: getElementSelector(element),
      impact: 'moderate',
      wcagLevel: 'AAA',
      wcagCriteria: ['1.4.6']
    });
  }
  
  return issues;
}

/**
 * Check for proper heading hierarchy
 */
export function checkHeadingHierarchy(container: Element = document.body): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
  
  let previousLevel = 0;
  
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    
    // Check for missing h1
    if (index === 0 && level !== 1) {
      issues.push({
        type: 'error',
        rule: 'heading-hierarchy',
        description: 'Page should start with an h1 heading',
        element: heading,
        selector: getElementSelector(heading),
        impact: 'serious',
        wcagLevel: 'AA',
        wcagCriteria: ['1.3.1', '2.4.6']
      });
    }
    
    // Check for skipped heading levels
    if (level > previousLevel + 1) {
      issues.push({
        type: 'error',
        rule: 'heading-hierarchy',
        description: `Heading level skipped from h${previousLevel} to h${level}`,
        element: heading,
        selector: getElementSelector(heading),
        impact: 'moderate',
        wcagLevel: 'AA',
        wcagCriteria: ['1.3.1', '2.4.6']
      });
    }
    
    // Check for empty headings
    if (!heading.textContent?.trim()) {
      issues.push({
        type: 'error',
        rule: 'empty-heading',
        description: 'Heading element is empty',
        element: heading,
        selector: getElementSelector(heading),
        impact: 'serious',
        wcagLevel: 'A',
        wcagCriteria: ['1.3.1', '2.4.6']
      });
    }
    
    previousLevel = level;
  });
  
  return issues;
}

/**
 * Check for missing alt text on images
 */
export function checkImageAltText(container: Element = document.body): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  const images = container.querySelectorAll('img');
  
  images.forEach(img => {
    const alt = img.getAttribute('alt');
    const role = img.getAttribute('role');
    
    // Skip decorative images
    if (role === 'presentation' || alt === '') {
      return;
    }
    
    if (!alt) {
      issues.push({
        type: 'error',
        rule: 'image-alt',
        description: 'Image missing alt attribute',
        element: img,
        selector: getElementSelector(img),
        impact: 'critical',
        wcagLevel: 'A',
        wcagCriteria: ['1.1.1']
      });
    } else if (alt.length > 125) {
      issues.push({
        type: 'warning',
        rule: 'image-alt-length',
        description: 'Alt text is very long (consider using longdesc or caption)',
        element: img,
        selector: getElementSelector(img),
        impact: 'minor',
        wcagLevel: 'A',
        wcagCriteria: ['1.1.1']
      });
    }
  });
  
  return issues;
}

/**
 * Check form accessibility
 */
export function checkFormAccessibility(container: Element = document.body): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  
  // Check form controls have labels
  const formControls = container.querySelectorAll('input, select, textarea');
  
  formControls.forEach(control => {
    const id = control.getAttribute('id');
    const ariaLabel = control.getAttribute('aria-label');
    const ariaLabelledby = control.getAttribute('aria-labelledby');
    const title = control.getAttribute('title');
    
    let hasLabel = false;
    
    if (id) {
      const label = container.querySelector(`label[for="${id}"]`);
      if (label) hasLabel = true;
    }
    
    if (ariaLabel || ariaLabelledby || title) {
      hasLabel = true;
    }
    
    // Check if control is inside a label
    const parentLabel = control.closest('label');
    if (parentLabel) hasLabel = true;
    
    if (!hasLabel) {
      issues.push({
        type: 'error',
        rule: 'form-label',
        description: 'Form control missing accessible label',
        element: control,
        selector: getElementSelector(control),
        impact: 'critical',
        wcagLevel: 'A',
        wcagCriteria: ['1.3.1', '3.3.2']
      });
    }
  });
  
  // Check for fieldsets with legends
  const fieldsets = container.querySelectorAll('fieldset');
  fieldsets.forEach(fieldset => {
    const legend = fieldset.querySelector('legend');
    if (!legend) {
      issues.push({
        type: 'error',
        rule: 'fieldset-legend',
        description: 'Fieldset missing legend',
        element: fieldset,
        selector: getElementSelector(fieldset),
        impact: 'serious',
        wcagLevel: 'A',
        wcagCriteria: ['1.3.1']
      });
    }
  });
  
  return issues;
}

/**
 * Check keyboard accessibility
 */
export function checkKeyboardAccessibility(container: Element = document.body): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  
  // Check for interactive elements without proper focus
  const interactiveElements = container.querySelectorAll(
    'a, button, input, select, textarea, [tabindex], [role="button"], [role="link"], [role="menuitem"]'
  );
  
  interactiveElements.forEach(element => {
    const tabindex = element.getAttribute('tabindex');
    const role = element.getAttribute('role');
    
    // Check for positive tabindex (anti-pattern)
    if (tabindex && parseInt(tabindex) > 0) {
      issues.push({
        type: 'warning',
        rule: 'positive-tabindex',
        description: 'Avoid positive tabindex values',
        element,
        selector: getElementSelector(element),
        impact: 'moderate',
        wcagLevel: 'A',
        wcagCriteria: ['2.4.3']
      });
    }
    
    // Check for elements that should be focusable but aren't
    if (role === 'button' || role === 'link') {
      const isFocusable = element.hasAttribute('tabindex') || 
                         element.tagName === 'BUTTON' || 
                         element.tagName === 'A';
      
      if (!isFocusable) {
        issues.push({
          type: 'error',
          rule: 'keyboard-focusable',
          description: 'Interactive element not keyboard focusable',
          element,
          selector: getElementSelector(element),
          impact: 'serious',
          wcagLevel: 'A',
          wcagCriteria: ['2.1.1']
        });
      }
    }
  });
  
  return issues;
}

/**
 * Check ARIA usage
 */
export function checkAriaUsage(container: Element = document.body): AccessibilityIssue[] {
  const issues: AccessibilityIssue[] = [];
  
  // Check for invalid ARIA attributes
  const elementsWithAria = container.querySelectorAll('[aria-label], [aria-labelledby], [aria-describedby], [role]');
  
  elementsWithAria.forEach(element => {
    const ariaLabelledby = element.getAttribute('aria-labelledby');
    const ariaDescribedby = element.getAttribute('aria-describedby');
    const role = element.getAttribute('role');
    
    // Check if referenced elements exist
    if (ariaLabelledby) {
      const ids = ariaLabelledby.split(' ');
      ids.forEach(id => {
        if (!container.querySelector(`#${id}`)) {
          issues.push({
            type: 'error',
            rule: 'aria-labelledby-valid',
            description: `aria-labelledby references non-existent element: ${id}`,
            element,
            selector: getElementSelector(element),
            impact: 'serious',
            wcagLevel: 'A',
            wcagCriteria: ['1.3.1']
          });
        }
      });
    }
    
    if (ariaDescribedby) {
      const ids = ariaDescribedby.split(' ');
      ids.forEach(id => {
        if (!container.querySelector(`#${id}`)) {
          issues.push({
            type: 'error',
            rule: 'aria-describedby-valid',
            description: `aria-describedby references non-existent element: ${id}`,
            element,
            selector: getElementSelector(element),
            impact: 'moderate',
            wcagLevel: 'A',
            wcagCriteria: ['1.3.1']
          });
        }
      });
    }
    
    // Check for invalid roles
    if (role) {
      const validRoles = [
        'alert', 'alertdialog', 'application', 'article', 'banner', 'button',
        'cell', 'checkbox', 'columnheader', 'combobox', 'complementary',
        'contentinfo', 'definition', 'dialog', 'directory', 'document',
        'feed', 'figure', 'form', 'grid', 'gridcell', 'group', 'heading',
        'img', 'link', 'list', 'listbox', 'listitem', 'log', 'main',
        'marquee', 'math', 'menu', 'menubar', 'menuitem', 'menuitemcheckbox',
        'menuitemradio', 'navigation', 'none', 'note', 'option', 'presentation',
        'progressbar', 'radio', 'radiogroup', 'region', 'row', 'rowgroup',
        'rowheader', 'scrollbar', 'search', 'searchbox', 'separator',
        'slider', 'spinbutton', 'status', 'switch', 'tab', 'table',
        'tablist', 'tabpanel', 'term', 'textbox', 'timer', 'toolbar',
        'tooltip', 'tree', 'treegrid', 'treeitem'
      ];
      
      if (!validRoles.includes(role)) {
        issues.push({
          type: 'error',
          rule: 'invalid-aria-role',
          description: `Invalid ARIA role: ${role}`,
          element,
          selector: getElementSelector(element),
          impact: 'serious',
          wcagLevel: 'A',
          wcagCriteria: ['4.1.2']
        });
      }
    }
  });
  
  return issues;
}

/**
 * Run comprehensive accessibility test
 */
export function runAccessibilityTest(container: Element = document.body): AccessibilityTestResult {
  const allIssues: AccessibilityIssue[] = [
    ...checkHeadingHierarchy(container),
    ...checkImageAltText(container),
    ...checkFormAccessibility(container),
    ...checkKeyboardAccessibility(container),
    ...checkAriaUsage(container)
  ];
  
  // Add color contrast checks for text elements
  const textElements = container.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, a, button, label');
  textElements.forEach(element => {
    allIssues.push(...checkColorContrast(element));
  });
  
  const summary = {
    total: allIssues.length,
    errors: allIssues.filter(issue => issue.type === 'error').length,
    warnings: allIssues.filter(issue => issue.type === 'warning').length,
    info: allIssues.filter(issue => issue.type === 'info').length
  };
  
  // Calculate accessibility score (0-100)
  const maxPossibleIssues = textElements.length + 50; // Rough estimate
  const weightedIssues = allIssues.reduce((total, issue) => {
    switch (issue.impact) {
      case 'critical': return total + 4;
      case 'serious': return total + 3;
      case 'moderate': return total + 2;
      case 'minor': return total + 1;
      default: return total + 1;
    }
  }, 0);
  
  const score = Math.max(0, Math.round(100 - (weightedIssues / maxPossibleIssues) * 100));
  
  return {
    passed: summary.errors === 0,
    issues: allIssues,
    summary,
    score
  };
}

/**
 * Generate accessibility report
 */
export function generateAccessibilityReport(result: AccessibilityTestResult): string {
  const { issues, summary, score } = result;
  
  let report = `# Accessibility Test Report\n\n`;
  report += `**Score:** ${score}/100\n`;
  report += `**Status:** ${result.passed ? '✅ PASSED' : '❌ FAILED'}\n\n`;
  
  report += `## Summary\n`;
  report += `- Total Issues: ${summary.total}\n`;
  report += `- Errors: ${summary.errors}\n`;
  report += `- Warnings: ${summary.warnings}\n`;
  report += `- Info: ${summary.info}\n\n`;
  
  if (issues.length > 0) {
    report += `## Issues\n\n`;
    
    const groupedIssues = issues.reduce((groups, issue) => {
      const key = issue.rule;
      if (!groups[key]) groups[key] = [];
      groups[key].push(issue);
      return groups;
    }, {} as Record<string, AccessibilityIssue[]>);
    
    Object.entries(groupedIssues).forEach(([rule, ruleIssues]) => {
      report += `### ${rule}\n`;
      ruleIssues.forEach(issue => {
        const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
        report += `${icon} **${issue.impact.toUpperCase()}** - ${issue.description}\n`;
        if (issue.selector) {
          report += `   - Element: \`${issue.selector}\`\n`;
        }
        report += `   - WCAG: ${issue.wcagLevel} (${issue.wcagCriteria.join(', ')})\n\n`;
      });
    });
  }
  
  return report;
}

/**
 * Helper functions
 */
function calculateContrastRatio(color1: string, color2: string): number {
  const rgb1 = parseColor(color1);
  const rgb2 = parseColor(color2);
  
  if (!rgb1 || !rgb2) return 1;
  
  const l1 = getRelativeLuminance(rgb1);
  const l2 = getRelativeLuminance(rgb2);
  
  const lighter = Math.max(l1, l2);
  const darker = Math.min(l1, l2);
  
  return (lighter + 0.05) / (darker + 0.05);
}

function parseColor(color: string): { r: number; g: number; b: number } | null {
  // Simple RGB parser - in production, use a more robust color parsing library
  const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (rgbMatch) {
    return {
      r: parseInt(rgbMatch[1]),
      g: parseInt(rgbMatch[2]),
      b: parseInt(rgbMatch[3])
    };
  }
  
  const hexMatch = color.match(/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
  if (hexMatch) {
    return {
      r: parseInt(hexMatch[1], 16),
      g: parseInt(hexMatch[2], 16),
      b: parseInt(hexMatch[3], 16)
    };
  }
  
  return null;
}

function getRelativeLuminance(rgb: { r: number; g: number; b: number }): number {
  const { r, g, b } = rgb;
  
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

function getElementSelector(element: Element): string {
  if (element.id) {
    return `#${element.id}`;
  }
  
  if (element.className) {
    const classes = element.className.split(' ').filter(c => c.trim());
    if (classes.length > 0) {
      return `${element.tagName.toLowerCase()}.${classes[0]}`;
    }
  }
  
  return element.tagName.toLowerCase();
}

/**
 * Test keyboard navigation
 */
export async function testKeyboardNavigation(container: Element = document.body): Promise<{
  focusableElements: Element[];
  issues: AccessibilityIssue[];
  tabOrder: Element[];
}> {
  const issues: AccessibilityIssue[] = [];
  const focusableElements = Array.from(container.querySelectorAll(
    'a[href], button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
  ));
  
  const tabOrder: Element[] = [];
  
  // Simulate tab navigation
  for (const element of focusableElements) {
    try {
      (element as HTMLElement).focus();
      
      if (document.activeElement === element) {
        tabOrder.push(element);
        
        // Check if element has visible focus indicator
        const computedStyle = window.getComputedStyle(element, ':focus');
        const outline = computedStyle.outline;
        const outlineWidth = computedStyle.outlineWidth;
        const boxShadow = computedStyle.boxShadow;
        
        if (outline === 'none' && outlineWidth === '0px' && !boxShadow.includes('inset')) {
          issues.push({
            type: 'warning',
            rule: 'focus-visible',
            description: 'Element may not have visible focus indicator',
            element,
            selector: getElementSelector(element),
            impact: 'moderate',
            wcagLevel: 'AA',
            wcagCriteria: ['2.4.7']
          });
        }
      } else {
        issues.push({
          type: 'error',
          rule: 'keyboard-focusable',
          description: 'Element cannot receive keyboard focus',
          element,
          selector: getElementSelector(element),
          impact: 'serious',
          wcagLevel: 'A',
          wcagCriteria: ['2.1.1']
        });
      }
    } catch (error) {
      issues.push({
        type: 'error',
        rule: 'focus-error',
        description: 'Error occurred while testing focus',
        element,
        selector: getElementSelector(element),
        impact: 'moderate',
        wcagLevel: 'A',
        wcagCriteria: ['2.1.1']
      });
    }
  }
  
  return {
    focusableElements,
    issues,
    tabOrder
  };
}