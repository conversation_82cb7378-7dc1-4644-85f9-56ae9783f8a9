import { json } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq, like, and, or } from 'drizzle-orm';
import { SecurityPresets } from '$lib/server/middleware/security';
import { ApiValidators } from '$lib/server/services/inputValidation';
import { z } from 'zod';

/**
 * Enhanced Admin Users API Endpoint
 * Demonstrates comprehensive security implementation
 */

// Validation schemas for different operations
const CreateUserSchema = z.object({
  username: z.string().min(3).max(20).regex(/^[a-zA-Z0-9_-]+$/),
  displayName: z.string().min(2).max(50),
  email: z.string().email().max(254),
  password: z.string().min(8).max(128),
  role: z.enum(['admin', 'moderator', 'user']).default('user'),
  bio: z.string().max(1000).optional(),
  isSimulated: z.boolean().default(false),
  simulatedPersonality: z.object({
    traits: z.array(z.string()).optional(),
    interests: z.array(z.string()).optional(),
    writingStyle: z.string().optional(),
    activityLevel: z.enum(['low', 'medium', 'high']).optional()
  }).optional()
});

const UpdateUserSchema = z.object({
  displayName: z.string().min(2).max(50).optional(),
  email: z.string().email().max(254).optional(),
  role: z.enum(['admin', 'moderator', 'user']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  bio: z.string().max(1000).optional(),
  simulatedPersonality: z.object({
    traits: z.array(z.string()).optional(),
    interests: z.array(z.string()).optional(),
    writingStyle: z.string().optional(),
    activityLevel: z.enum(['low', 'medium', 'high']).optional()
  }).optional()
});

const QuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  search: z.string().max(100).optional(),
  role: z.enum(['admin', 'moderator', 'user']).optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  isSimulated: z.coerce.boolean().optional(),
  sortBy: z.enum(['username', 'displayName', 'email', 'createdAt', 'lastActiveAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});

/**
 * GET /api/admin/users
 * List users with filtering, searching, and pagination
 */
export const GET: RequestHandler = SecurityPresets.admin({
  auditLog: true
})(async (event) => {
  try {
    // Parse and validate query parameters
    const url = new URL(event.request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const query = QuerySchema.parse(queryParams);

    // Build database query with filters
    let dbQuery = db.select({
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      email: users.email,
      role: users.role,
      status: users.status,
      isSimulated: users.isSimulated,
      lastActiveAt: users.lastActiveAt,
      createdAt: users.createdAt,
      updatedAt: users.updatedAt
    }).from(users);

    // Apply filters
    const conditions = [];

    if (query.search) {
      conditions.push(
        or(
          like(users.username, `%${query.search}%`),
          like(users.displayName, `%${query.search}%`),
          like(users.email, `%${query.search}%`)
        )
      );
    }

    if (query.role) {
      conditions.push(eq(users.role, query.role));
    }

    if (query.status) {
      conditions.push(eq(users.status, query.status));
    }

    if (query.isSimulated !== undefined) {
      conditions.push(eq(users.isSimulated, query.isSimulated));
    }

    if (conditions.length > 0) {
      dbQuery = dbQuery.where(and(...conditions));
    }

    // Apply sorting
    const sortColumn = users[query.sortBy as keyof typeof users];
    if (query.sortOrder === 'desc') {
      dbQuery = dbQuery.orderBy(desc(sortColumn));
    } else {
      dbQuery = dbQuery.orderBy(asc(sortColumn));
    }

    // Apply pagination
    const offset = (query.page - 1) * query.limit;
    dbQuery = dbQuery.limit(query.limit).offset(offset);

    // Execute query
    const userList = await dbQuery;

    // Get total count for pagination
    const totalQuery = db.select({ count: count() }).from(users);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / query.limit);
    const hasNextPage = query.page < totalPages;
    const hasPrevPage = query.page > 1;

    return json({
      success: true,
      data: userList,
      pagination: {
        page: query.page,
        limit: query.limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestedBy: event.locals.user?.id
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return json({
      success: false,
      error: 'Failed to fetch users',
      code: 'FETCH_USERS_FAILED'
    }, { status: 500 });
  }
});

/**
 * POST /api/admin/users
 * Create a new user account
 */
export const POST: RequestHandler = SecurityPresets.admin({
  validateInput: CreateUserSchema,
  auditLog: true
})(async (event) => {
  try {
    const userData = event.locals.validatedData;

    // Check for existing username/email
    const existingUser = await db.select()
      .from(users)
      .where(
        or(
          eq(users.username, userData.username),
          eq(users.email, userData.email)
        )
      )
      .limit(1);

    if (existingUser.length > 0) {
      return json({
        success: false,
        error: 'Username or email already exists',
        code: 'USER_EXISTS'
      }, { status: 409 });
    }

    // Hash password (in real implementation, use proper password hashing)
    const passwordHash = await hashPassword(userData.password);

    // Create user record
    const newUser = {
      username: userData.username,
      displayName: userData.displayName,
      email: userData.email,
      passwordHash,
      role: userData.role,
      bio: userData.bio,
      isSimulated: userData.isSimulated,
      simulatedPersonality: userData.simulatedPersonality ? JSON.stringify(userData.simulatedPersonality) : null,
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const [createdUser] = await db.insert(users).values(newUser).returning({
      id: users.id,
      username: users.username,
      displayName: users.displayName,
      email: users.email,
      role: users.role,
      status: users.status,
      isSimulated: users.isSimulated,
      createdAt: users.createdAt
    });

    // Log audit event
    await logAuditEvent({
      adminUserId: event.locals.user!.id,
      action: 'create_user',
      targetType: 'user',
      targetId: createdUser.id,
      details: {
        username: createdUser.username,
        role: createdUser.role,
        isSimulated: createdUser.isSimulated
      },
      ipAddress: event.getClientAddress(),
      userAgent: event.request.headers.get('user-agent')
    });

    return json({
      success: true,
      data: createdUser,
      message: 'User created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating user:', error);
    return json({
      success: false,
      error: 'Failed to create user',
      code: 'CREATE_USER_FAILED'
    }, { status: 500 });
  }
});

/**
 * PUT /api/admin/users/[id]
 * Update an existing user
 */
export const PUT: RequestHandler = SecurityPresets.admin({
  validateInput: UpdateUserSchema,
  auditLog: true
})(async (event) => {
  try {
    const userId = parseInt(event.params.id as string);
    if (isNaN(userId)) {
      return json({
        success: false,
        error: 'Invalid user ID',
        code: 'INVALID_USER_ID'
      }, { status: 400 });
    }

    const updateData = event.locals.validatedData;

    // Get existing user for audit trail
    const [existingUser] = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!existingUser) {
      return json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      }, { status: 404 });
    }

    // Prevent self-demotion for admins
    if (existingUser.role === 'admin' && 
        updateData.role && 
        updateData.role !== 'admin' && 
        existingUser.id === event.locals.user!.id) {
      return json({
        success: false,
        error: 'Cannot demote yourself from admin role',
        code: 'SELF_DEMOTION_FORBIDDEN'
      }, { status: 403 });
    }

    // Update user
    const updatedFields = {
      ...updateData,
      simulatedPersonality: updateData.simulatedPersonality ? 
        JSON.stringify(updateData.simulatedPersonality) : undefined,
      updatedAt: new Date().toISOString()
    };

    const [updatedUser] = await db.update(users)
      .set(updatedFields)
      .where(eq(users.id, userId))
      .returning({
        id: users.id,
        username: users.username,
        displayName: users.displayName,
        email: users.email,
        role: users.role,
        status: users.status,
        isSimulated: users.isSimulated,
        updatedAt: users.updatedAt
      });

    // Log audit event
    await logAuditEvent({
      adminUserId: event.locals.user!.id,
      action: 'update_user',
      targetType: 'user',
      targetId: userId,
      targetUserId: userId,
      details: {
        before: {
          displayName: existingUser.displayName,
          role: existingUser.role,
          status: existingUser.status
        },
        after: updateData
      },
      ipAddress: event.getClientAddress(),
      userAgent: event.request.headers.get('user-agent')
    });

    return json({
      success: true,
      data: updatedUser,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Error updating user:', error);
    return json({
      success: false,
      error: 'Failed to update user',
      code: 'UPDATE_USER_FAILED'
    }, { status: 500 });
  }
});

/**
 * DELETE /api/admin/users/[id]
 * Delete a user account
 */
export const DELETE: RequestHandler = SecurityPresets.admin({
  auditLog: true
})(async (event) => {
  try {
    const userId = parseInt(event.params.id as string);
    if (isNaN(userId)) {
      return json({
        success: false,
        error: 'Invalid user ID',
        code: 'INVALID_USER_ID'
      }, { status: 400 });
    }

    // Prevent self-deletion
    if (userId === event.locals.user!.id) {
      return json({
        success: false,
        error: 'Cannot delete your own account',
        code: 'SELF_DELETION_FORBIDDEN'
      }, { status: 403 });
    }

    // Get user for audit trail
    const [userToDelete] = await db.select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!userToDelete) {
      return json({
        success: false,
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      }, { status: 404 });
    }

    // Delete user
    await db.delete(users).where(eq(users.id, userId));

    // Log audit event
    await logAuditEvent({
      adminUserId: event.locals.user!.id,
      action: 'delete_user',
      targetType: 'user',
      targetId: userId,
      targetUserId: userId,
      details: {
        username: userToDelete.username,
        displayName: userToDelete.displayName,
        role: userToDelete.role
      },
      ipAddress: event.getClientAddress(),
      userAgent: event.request.headers.get('user-agent')
    });

    return json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting user:', error);
    return json({
      success: false,
      error: 'Failed to delete user',
      code: 'DELETE_USER_FAILED'
    }, { status: 500 });
  }
});

// Helper functions
async function hashPassword(password: string): Promise<string> {
  // In a real implementation, use bcrypt or similar
  // This is just a placeholder
  return `hashed_${password}`;
}

async function logAuditEvent(eventData: any): Promise<void> {
  // In a real implementation, this would write to the audit_logs table
  console.log('AUDIT EVENT:', JSON.stringify(eventData, null, 2));
}

// Import necessary functions from drizzle-orm
import { desc, asc, count } from 'drizzle-orm';