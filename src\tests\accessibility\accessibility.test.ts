import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, cleanup } from '@testing-library/svelte';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';

// Import components to test
import ErrorBoundary from '$lib/components/ErrorBoundary.svelte';
import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
import SkipLinks from '$lib/components/SkipLinks.svelte';
import AccessibilityAnnouncer from '$lib/components/AccessibilityAnnouncer.svelte';

// Import accessibility testing utilities
import {
  runAccessibilityTest,
  checkColorContrast,
  checkHeadingHierarchy,
  checkImageAltText,
  checkFormAccessibility,
  checkKeyboardAccessibility,
  testKeyboardNavigation,
  generateAccessibilityReport
} from '$lib/utils/accessibilityTesting';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

describe('Accessibility Testing Suite', () => {
  beforeEach(() => {
    // Set up DOM environment
    document.body.innerHTML = '';
    
    // Mock window.matchMedia for theme testing
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: (query: string) => ({
        matches: query.includes('dark'),
        media: query,
        onchange: null,
        addListener: () => {},
        removeListener: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
        dispatchEvent: () => {},
      }),
    });
  });

  afterEach(() => {
    cleanup();
  });

  describe('Component Accessibility', () => {
    describe('ErrorBoundary Component', () => {
      it('should have proper ARIA attributes', () => {
        const mockError = new Error('Test error');
        render(ErrorBoundary, {
          props: {
            title: 'Test Error',
            message: 'This is a test error message',
            error: mockError,
            showDetails: true
          }
        });

        const errorElement = screen.getByRole('alert');
        expect(errorElement).toBeInTheDocument();
        expect(errorElement).toHaveAttribute('aria-live', 'assertive');
        expect(errorElement).toHaveAttribute('tabindex', '-1');
      });

      it('should support keyboard navigation', async () => {
        const mockRetry = vi.fn();
        render(ErrorBoundary, {
          props: {
            title: 'Test Error',
            message: 'This is a test error message',
            error: new Error('Test error'),
            onRetry: mockRetry,
            showDetails: true
          }
        });

        const retryButton = screen.getByRole('button', { name: /try again/i });
        expect(retryButton).toBeInTheDocument();

        // Test keyboard activation
        retryButton.focus();
        fireEvent.keyDown(retryButton, { key: 'Enter' });
        expect(mockRetry).toHaveBeenCalled();
      });

      it('should have no accessibility violations', async () => {
        const { container } = render(ErrorBoundary, {
          props: {
            title: 'Test Error',
            message: 'This is a test error message',
            error: new Error('Test error'),
            showDetails: true
          }
        });

        const results = await axe(container);
        expect(results).toHaveNoViolations();
      });

      it('should announce error to screen readers', () => {
        render(ErrorBoundary, {
          props: {
            title: 'Critical Error',
            message: 'System failure detected',
            error: new Error('System error')
          }
        });

        const alertElement = screen.getByRole('alert');
        expect(alertElement).toHaveAttribute('aria-live', 'assertive');
        expect(alertElement).toHaveTextContent('Critical Error');
      });
    });

    describe('LoadingSpinner Component', () => {
      it('should have proper accessibility attributes', () => {
        render(LoadingSpinner, {
          props: {
            message: 'Loading content...',
            size: 'medium'
          }
        });

        const statusElement = screen.getByRole('status');
        expect(statusElement).toBeInTheDocument();
        expect(statusElement).toHaveAttribute('aria-live', 'polite');
        expect(statusElement).toHaveTextContent('Loading content...');
      });

      it('should respect reduced motion preferences', () => {
        // Mock prefers-reduced-motion
        Object.defineProperty(window, 'matchMedia', {
          writable: true,
          value: (query: string) => ({
            matches: query.includes('prefers-reduced-motion: reduce'),
            media: query,
            onchange: null,
            addListener: () => {},
            removeListener: () => {},
            addEventListener: () => {},
            removeEventListener: () => {},
            dispatchEvent: () => {},
          }),
        });

        const { container } = render(LoadingSpinner, {
          props: { message: 'Loading...' }
        });

        const spinner = container.querySelector('.spinner');
        expect(spinner).toBeInTheDocument();
        
        // In reduced motion mode, animation should be disabled
        const computedStyle = window.getComputedStyle(spinner!);
        // This would need actual CSS testing in a real browser environment
      });

      it('should have no accessibility violations', async () => {
        const { container } = render(LoadingSpinner, {
          props: { message: 'Loading data...' }
        });

        const results = await axe(container);
        expect(results).toHaveNoViolations();
      });
    });

    describe('SkipLinks Component', () => {
      it('should render skip links with proper attributes', () => {
        const links = [
          { href: '#main', label: 'Skip to main content' },
          { href: '#nav', label: 'Skip to navigation' }
        ];

        render(SkipLinks, { props: { links } });

        const skipLinksContainer = screen.getByLabelText('Skip navigation links');
        expect(skipLinksContainer).toBeInTheDocument();

        const mainLink = screen.getByRole('link', { name: 'Skip to main content' });
        const navLink = screen.getByRole('link', { name: 'Skip to navigation' });

        expect(mainLink).toHaveAttribute('href', '#main');
        expect(navLink).toHaveAttribute('href', '#nav');
      });

      it('should be keyboard accessible', () => {
        const links = [{ href: '#main', label: 'Skip to main content' }];
        render(SkipLinks, { props: { links } });

        const skipLink = screen.getByRole('link', { name: 'Skip to main content' });
        
        // Skip links should be focusable
        skipLink.focus();
        expect(document.activeElement).toBe(skipLink);
      });

      it('should have no accessibility violations', async () => {
        const { container } = render(SkipLinks, {
          props: {
            links: [
              { href: '#main', label: 'Skip to main content' },
              { href: '#nav', label: 'Skip to navigation' }
            ]
          }
        });

        const results = await axe(container);
        expect(results).toHaveNoViolations();
      });
    });

    describe('AccessibilityAnnouncer Component', () => {
      it('should create announcement region', () => {
        render(AccessibilityAnnouncer, {
          props: {
            message: 'Test announcement',
            priority: 'assertive'
          }
        });

        const announcer = screen.getByRole('status');
        expect(announcer).toBeInTheDocument();
        expect(announcer).toHaveAttribute('aria-live', 'assertive');
        expect(announcer).toHaveAttribute('aria-atomic', 'true');
      });

      it('should be screen reader only', () => {
        const { container } = render(AccessibilityAnnouncer);
        
        const announcer = container.querySelector('.sr-only');
        expect(announcer).toBeInTheDocument();
        
        // Check that it's visually hidden but accessible to screen readers
        const computedStyle = window.getComputedStyle(announcer!);
        expect(computedStyle.position).toBe('absolute');
        expect(computedStyle.width).toBe('1px');
        expect(computedStyle.height).toBe('1px');
      });
    });
  });

  describe('Accessibility Testing Utilities', () => {
    describe('Color Contrast Testing', () => {
      it('should detect insufficient color contrast', () => {
        document.body.innerHTML = `
          <div id="test-element" style="color: #999; background-color: #fff;">
            Low contrast text
          </div>
        `;

        const element = document.getElementById('test-element')!;
        const issues = checkColorContrast(element);

        expect(issues.length).toBeGreaterThan(0);
        expect(issues[0].rule).toBe('color-contrast');
        expect(issues[0].type).toBe('error');
      });

      it('should pass for sufficient color contrast', () => {
        document.body.innerHTML = `
          <div id="test-element" style="color: #000; background-color: #fff;">
            High contrast text
          </div>
        `;

        const element = document.getElementById('test-element')!;
        const issues = checkColorContrast(element);

        expect(issues.length).toBe(0);
      });
    });

    describe('Heading Hierarchy Testing', () => {
      it('should detect improper heading hierarchy', () => {
        document.body.innerHTML = `
          <h3>Skipped h1 and h2</h3>
          <h1>Main heading after h3</h1>
        `;

        const issues = checkHeadingHierarchy();

        expect(issues.length).toBeGreaterThan(0);
        expect(issues.some(issue => issue.rule === 'heading-hierarchy')).toBe(true);
      });

      it('should pass for proper heading hierarchy', () => {
        document.body.innerHTML = `
          <h1>Main Heading</h1>
          <h2>Section Heading</h2>
          <h3>Subsection Heading</h3>
        `;

        const issues = checkHeadingHierarchy();
        const hierarchyIssues = issues.filter(issue => issue.rule === 'heading-hierarchy');

        expect(hierarchyIssues.length).toBe(0);
      });

      it('should detect empty headings', () => {
        document.body.innerHTML = `
          <h1></h1>
          <h2>   </h2>
        `;

        const issues = checkHeadingHierarchy();
        const emptyHeadingIssues = issues.filter(issue => issue.rule === 'empty-heading');

        expect(emptyHeadingIssues.length).toBe(2);
      });
    });

    describe('Image Alt Text Testing', () => {
      it('should detect missing alt text', () => {
        document.body.innerHTML = `
          <img src="test.jpg" />
          <img src="test2.jpg" alt="" role="presentation" />
        `;

        const issues = checkImageAltText();
        const altIssues = issues.filter(issue => issue.rule === 'image-alt');

        expect(altIssues.length).toBe(1); // Only the first image should have an issue
      });

      it('should detect overly long alt text', () => {
        const longAlt = 'A'.repeat(150);
        document.body.innerHTML = `
          <img src="test.jpg" alt="${longAlt}" />
        `;

        const issues = checkImageAltText();
        const lengthIssues = issues.filter(issue => issue.rule === 'image-alt-length');

        expect(lengthIssues.length).toBe(1);
      });
    });

    describe('Form Accessibility Testing', () => {
      it('should detect unlabeled form controls', () => {
        document.body.innerHTML = `
          <input type="text" />
          <select>
            <option>Option 1</option>
          </select>
        `;

        const issues = checkFormAccessibility();
        const labelIssues = issues.filter(issue => issue.rule === 'form-label');

        expect(labelIssues.length).toBe(2);
      });

      it('should pass for properly labeled form controls', () => {
        document.body.innerHTML = `
          <label for="name">Name:</label>
          <input type="text" id="name" />
          
          <label>
            Email:
            <input type="email" />
          </label>
          
          <input type="submit" aria-label="Submit form" />
        `;

        const issues = checkFormAccessibility();
        const labelIssues = issues.filter(issue => issue.rule === 'form-label');

        expect(labelIssues.length).toBe(0);
      });

      it('should detect fieldsets without legends', () => {
        document.body.innerHTML = `
          <fieldset>
            <input type="radio" name="choice" value="1" />
            <input type="radio" name="choice" value="2" />
          </fieldset>
        `;

        const issues = checkFormAccessibility();
        const fieldsetIssues = issues.filter(issue => issue.rule === 'fieldset-legend');

        expect(fieldsetIssues.length).toBe(1);
      });
    });

    describe('Keyboard Accessibility Testing', () => {
      it('should detect positive tabindex values', () => {
        document.body.innerHTML = `
          <button tabindex="1">Bad Button</button>
          <button tabindex="0">Good Button</button>
        `;

        const issues = checkKeyboardAccessibility();
        const tabindexIssues = issues.filter(issue => issue.rule === 'positive-tabindex');

        expect(tabindexIssues.length).toBe(1);
      });

      it('should detect non-focusable interactive elements', () => {
        document.body.innerHTML = `
          <div role="button">Not focusable button</div>
          <span role="link">Not focusable link</span>
        `;

        const issues = checkKeyboardAccessibility();
        const focusIssues = issues.filter(issue => issue.rule === 'keyboard-focusable');

        expect(focusIssues.length).toBe(2);
      });
    });

    describe('Comprehensive Accessibility Testing', () => {
      it('should run complete accessibility test', () => {
        document.body.innerHTML = `
          <main>
            <h1>Main Heading</h1>
            <p style="color: #000; background: #fff;">Good contrast text</p>
            <img src="test.jpg" alt="Test image" />
            <form>
              <label for="email">Email:</label>
              <input type="email" id="email" />
              <button type="submit">Submit</button>
            </form>
          </main>
        `;

        const result = runAccessibilityTest();

        expect(result).toHaveProperty('passed');
        expect(result).toHaveProperty('issues');
        expect(result).toHaveProperty('summary');
        expect(result).toHaveProperty('score');
        expect(result.score).toBeGreaterThanOrEqual(0);
        expect(result.score).toBeLessThanOrEqual(100);
      });

      it('should generate accessibility report', () => {
        document.body.innerHTML = `
          <h3>Bad heading hierarchy</h3>
          <img src="test.jpg" />
        `;

        const result = runAccessibilityTest();
        const report = generateAccessibilityReport(result);

        expect(report).toContain('# Accessibility Test Report');
        expect(report).toContain('Score:');
        expect(report).toContain('Issues');
        expect(typeof report).toBe('string');
      });
    });

    describe('Keyboard Navigation Testing', () => {
      it('should test keyboard navigation flow', async () => {
        document.body.innerHTML = `
          <button>Button 1</button>
          <a href="#test">Link 1</a>
          <input type="text" />
          <button>Button 2</button>
        `;

        const result = await testKeyboardNavigation();

        expect(result.focusableElements.length).toBe(4);
        expect(result.tabOrder.length).toBeGreaterThan(0);
        expect(Array.isArray(result.issues)).toBe(true);
      });
    });
  });

  describe('Theme Accessibility', () => {
    it('should maintain contrast ratios in dark theme', () => {
      document.documentElement.setAttribute('data-theme', 'dark');
      
      document.body.innerHTML = `
        <div style="color: var(--color-text-primary); background: var(--color-bg-primary);">
          Dark theme text
        </div>
      `;

      const element = document.body.firstElementChild!;
      const issues = checkColorContrast(element);

      // Should have no critical contrast issues
      const criticalIssues = issues.filter(issue => 
        issue.type === 'error' && issue.rule === 'color-contrast'
      );
      expect(criticalIssues.length).toBe(0);
    });

    it('should support high contrast mode', () => {
      // Mock high contrast media query
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: (query: string) => ({
          matches: query.includes('prefers-contrast: high'),
          media: query,
          onchange: null,
          addListener: () => {},
          removeListener: () => {},
          addEventListener: () => {},
          removeEventListener: () => {},
          dispatchEvent: () => {},
        }),
      });

      // Test that high contrast styles would be applied
      // This would require actual CSS testing in a real browser
      expect(window.matchMedia('(prefers-contrast: high)').matches).toBe(true);
    });
  });

  describe('Error Handling Accessibility', () => {
    it('should announce errors to screen readers', () => {
      render(ErrorBoundary, {
        props: {
          title: 'Form Error',
          message: 'Please correct the following errors',
          error: new Error('Validation failed')
        }
      });

      const errorAlert = screen.getByRole('alert');
      expect(errorAlert).toHaveAttribute('aria-live', 'assertive');
      expect(errorAlert).toHaveTextContent('Form Error');
    });

    it('should provide clear error recovery options', () => {
      const mockRetry = vi.fn();
      const mockReport = vi.fn();

      render(ErrorBoundary, {
        props: {
          title: 'Network Error',
          message: 'Failed to load data',
          error: new Error('Network timeout'),
          onRetry: mockRetry,
          onReport: mockReport
        }
      });

      const retryButton = screen.getByRole('button', { name: /try again/i });
      const reportButton = screen.getByRole('button', { name: /report issue/i });

      expect(retryButton).toBeInTheDocument();
      expect(reportButton).toBeInTheDocument();

      // Both buttons should be keyboard accessible
      retryButton.focus();
      expect(document.activeElement).toBe(retryButton);

      reportButton.focus();
      expect(document.activeElement).toBe(reportButton);
    });
  });
});

// Mock functions for testing
const vi = {
  fn: () => {
    const mockFn = (...args: any[]) => mockFn.mock.calls.push(args);
    mockFn.mock = { calls: [] as any[][] };
    return mockFn;
  }
};