import type { RequestEvent } from '@sveltejs/kit';
import { error, json } from '@sveltejs/kit';
import { checkRateLimit, recordRequest } from '../services/rateLimiting';
import { ApiValidators, detectSuspiciousContent } from '../services/inputValidation';
import { z } from 'zod';

/**
 * Enhanced security middleware for FWFC API endpoints
 * Implements comprehensive security checks and validation
 */

export interface SecurityOptions {
  requireAuth?: boolean;
  requireRole?: 'admin' | 'moderator' | 'user';
  rateLimit?: {
    key: string;
    identifier?: (event: RequestEvent) => string;
  };
  validateInput?: z.ZodSchema<any>;
  checkSuspiciousContent?: boolean;
  auditLog?: boolean;
  csrfProtection?: boolean;
}

/**
 * Main security middleware function
 */
export function withSecurity(options: SecurityOptions = {}) {
  return async (event: RequestEvent, handler: () => Promise<Response>) => {
    try {
      // 1. CSRF Protection
      if (options.csrfProtection && ['POST', 'PUT', 'DELETE', 'PATCH'].includes(event.request.method)) {
        await validateCSRFToken(event);
      }

      // 2. Rate Limiting
      if (options.rateLimit) {
        await checkRequestRateLimit(event, options.rateLimit);
      }

      // 3. Authentication Check
      if (options.requireAuth) {
        await validateAuthentication(event);
      }

      // 4. Authorization Check
      if (options.requireRole) {
        await validateAuthorization(event, options.requireRole);
      }

      // 5. Input Validation
      if (options.validateInput && ['POST', 'PUT', 'PATCH'].includes(event.request.method)) {
        await validateRequestInput(event, options.validateInput, options.checkSuspiciousContent);
      }

      // 6. Execute the handler
      const response = await handler();

      // 7. Audit Logging
      if (options.auditLog) {
        await logAuditEvent(event, response, 'success');
      }

      // 8. Record successful request for rate limiting
      if (options.rateLimit) {
        const identifier = options.rateLimit.identifier?.(event) || getClientIdentifier(event);
        recordRequest(options.rateLimit.key, identifier, true, event.locals.user?.id);
      }

      return response;

    } catch (err) {
      // Handle security errors
      const securityError = err as SecurityError;
      
      // Record failed request for rate limiting
      if (options.rateLimit) {
        const identifier = options.rateLimit.identifier?.(event) || getClientIdentifier(event);
        recordRequest(options.rateLimit.key, identifier, false, event.locals.user?.id);
      }

      // Audit log security failures
      if (options.auditLog) {
        await logAuditEvent(event, null, 'security_failure', securityError);
      }

      // Return appropriate error response
      return handleSecurityError(securityError);
    }
  };
}

/**
 * CSRF Token Validation
 */
async function validateCSRFToken(event: RequestEvent): Promise<void> {
  const token = event.request.headers.get('x-csrf-token') || 
                event.cookies.get('csrf-token');
  
  if (!token) {
    throw new SecurityError('CSRF token missing', 403, 'CSRF_TOKEN_MISSING');
  }

  // In a real implementation, validate the token against a stored value
  // For now, we'll do a basic check
  const expectedToken = event.cookies.get('csrf-token-expected');
  if (token !== expectedToken) {
    throw new SecurityError('Invalid CSRF token', 403, 'CSRF_TOKEN_INVALID');
  }
}

/**
 * Rate Limit Check
 */
async function checkRequestRateLimit(
  event: RequestEvent, 
  rateLimitConfig: { key: string; identifier?: (event: RequestEvent) => string }
): Promise<void> {
  const identifier = rateLimitConfig.identifier?.(event) || getClientIdentifier(event);
  const userId = event.locals.user?.id;
  
  const result = checkRateLimit(rateLimitConfig.key, identifier, userId);
  
  if (!result.allowed) {
    throw new SecurityError(
      'Rate limit exceeded', 
      429, 
      'RATE_LIMIT_EXCEEDED',
      { resetTime: result.resetTime, remaining: result.remaining }
    );
  }
}

/**
 * Authentication Validation
 */
async function validateAuthentication(event: RequestEvent): Promise<void> {
  if (!event.locals.user) {
    throw new SecurityError('Authentication required', 401, 'AUTH_REQUIRED');
  }

  // Check if user account is active
  if (event.locals.user.status !== 'active') {
    throw new SecurityError('Account suspended', 403, 'ACCOUNT_SUSPENDED');
  }
}

/**
 * Authorization Validation
 */
async function validateAuthorization(
  event: RequestEvent, 
  requiredRole: 'admin' | 'moderator' | 'user'
): Promise<void> {
  const user = event.locals.user;
  if (!user) {
    throw new SecurityError('Authentication required', 401, 'AUTH_REQUIRED');
  }

  const roleHierarchy = { admin: 3, moderator: 2, user: 1 };
  const userLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0;
  const requiredLevel = roleHierarchy[requiredRole];

  if (userLevel < requiredLevel) {
    throw new SecurityError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
  }
}

/**
 * Input Validation
 */
async function validateRequestInput(
  event: RequestEvent, 
  schema: z.ZodSchema<any>,
  checkSuspicious = false
): Promise<void> {
  let requestData: any;

  try {
    const contentType = event.request.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      requestData = await event.request.json();
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      const formData = await event.request.formData();
      requestData = Object.fromEntries(formData.entries());
    } else {
      throw new SecurityError('Unsupported content type', 400, 'UNSUPPORTED_CONTENT_TYPE');
    }
  } catch (err) {
    throw new SecurityError('Invalid request body', 400, 'INVALID_REQUEST_BODY');
  }

  // Validate against schema
  const validation = schema.safeParse(requestData);
  if (!validation.success) {
    const errors = validation.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    );
    throw new SecurityError(
      'Validation failed', 
      400, 
      'VALIDATION_FAILED',
      { errors }
    );
  }

  // Check for suspicious content
  if (checkSuspicious) {
    const suspiciousCheck = await checkForSuspiciousContent(validation.data);
    if (suspiciousCheck.suspicious && suspiciousCheck.riskLevel === 'high') {
      throw new SecurityError(
        'Potentially malicious content detected', 
        400, 
        'SUSPICIOUS_CONTENT',
        { reasons: suspiciousCheck.reasons }
      );
    }
  }

  // Store validated data for use in handler
  event.locals.validatedData = validation.data;
}

/**
 * Check for suspicious content in request data
 */
async function checkForSuspiciousContent(data: any): Promise<{
  suspicious: boolean;
  reasons: string[];
  riskLevel: 'low' | 'medium' | 'high';
}> {
  const reasons: string[] = [];
  let riskLevel: 'low' | 'medium' | 'high' = 'low';

  // Check string fields for suspicious patterns
  const checkValue = (value: any, path = ''): void => {
    if (typeof value === 'string') {
      const suspiciousCheck = detectSuspiciousContent(value);
      if (suspiciousCheck.suspicious) {
        reasons.push(...suspiciousCheck.reasons.map(reason => `${path}: ${reason}`));
        if (suspiciousCheck.riskLevel === 'high') {
          riskLevel = 'high';
        } else if (suspiciousCheck.riskLevel === 'medium' && riskLevel !== 'high') {
          riskLevel = 'medium';
        }
      }
    } else if (typeof value === 'object' && value !== null) {
      Object.entries(value).forEach(([key, val]) => {
        checkValue(val, path ? `${path}.${key}` : key);
      });
    }
  };

  checkValue(data);

  return {
    suspicious: reasons.length > 0,
    reasons,
    riskLevel
  };
}

/**
 * Audit Event Logging
 */
async function logAuditEvent(
  event: RequestEvent,
  response: Response | null,
  status: 'success' | 'security_failure',
  error?: SecurityError
): Promise<void> {
  const auditData = {
    timestamp: new Date().toISOString(),
    method: event.request.method,
    url: event.url.pathname,
    userAgent: event.request.headers.get('user-agent'),
    ip: getClientIP(event),
    userId: event.locals.user?.id,
    status,
    responseStatus: response?.status,
    error: error ? {
      code: error.code,
      message: error.message,
      details: error.details
    } : undefined
  };

  // In a real implementation, this would write to a database or logging service
  console.log('AUDIT:', JSON.stringify(auditData, null, 2));
}

/**
 * Security Error Class
 */
class SecurityError extends Error {
  constructor(
    message: string,
    public status: number,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'SecurityError';
  }
}

/**
 * Handle Security Errors
 */
function handleSecurityError(err: SecurityError): Response {
  const errorResponse = {
    error: err.message,
    code: err.code,
    ...(err.details && { details: err.details })
  };

  // Add rate limit headers if applicable
  if (err.code === 'RATE_LIMIT_EXCEEDED' && err.details) {
    const headers = new Headers();
    headers.set('Retry-After', Math.ceil((err.details.resetTime - Date.now()) / 1000).toString());
    headers.set('X-RateLimit-Remaining', err.details.remaining?.toString() || '0');
    
    return new Response(JSON.stringify(errorResponse), {
      status: err.status,
      headers: {
        'Content-Type': 'application/json',
        ...Object.fromEntries(headers.entries())
      }
    });
  }

  return new Response(JSON.stringify(errorResponse), {
    status: err.status,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * Utility Functions
 */
function getClientIdentifier(event: RequestEvent): string {
  // Use IP address as primary identifier
  const ip = getClientIP(event);
  const userAgent = event.request.headers.get('user-agent') || '';
  
  // Create a hash of IP + User Agent for better uniqueness
  return `${ip}:${userAgent.slice(0, 50)}`;
}

function getClientIP(event: RequestEvent): string {
  // Check various headers for the real IP address
  const forwarded = event.request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  const realIP = event.request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  // Fallback to connection IP (may be proxy IP)
  return event.getClientAddress();
}

/**
 * Convenience functions for common security patterns
 */
export const SecurityPresets = {
  // Public API endpoint (no auth required)
  public: (options: Partial<SecurityOptions> = {}) => withSecurity({
    rateLimit: { key: 'public_api', identifier: getClientIdentifier },
    validateInput: options.validateInput,
    checkSuspiciousContent: true,
    ...options
  }),

  // User-authenticated endpoint
  authenticated: (options: Partial<SecurityOptions> = {}) => withSecurity({
    requireAuth: true,
    requireRole: 'user',
    rateLimit: { key: 'user_api', identifier: (event) => event.locals.user?.id.toString() || getClientIdentifier(event) },
    validateInput: options.validateInput,
    checkSuspiciousContent: true,
    auditLog: true,
    csrfProtection: true,
    ...options
  }),

  // Moderator-only endpoint
  moderator: (options: Partial<SecurityOptions> = {}) => withSecurity({
    requireAuth: true,
    requireRole: 'moderator',
    rateLimit: { key: 'moderator_api', identifier: (event) => event.locals.user?.id.toString() || getClientIdentifier(event) },
    validateInput: options.validateInput,
    checkSuspiciousContent: true,
    auditLog: true,
    csrfProtection: true,
    ...options
  }),

  // Admin-only endpoint
  admin: (options: Partial<SecurityOptions> = {}) => withSecurity({
    requireAuth: true,
    requireRole: 'admin',
    rateLimit: { key: 'admin_api', identifier: (event) => event.locals.user?.id.toString() || getClientIdentifier(event) },
    validateInput: options.validateInput,
    checkSuspiciousContent: true,
    auditLog: true,
    csrfProtection: true,
    ...options
  }),

  // AI content generation endpoint
  aiGeneration: (options: Partial<SecurityOptions> = {}) => withSecurity({
    requireAuth: true,
    requireRole: 'admin',
    rateLimit: { key: 'ai_generation_per_user', identifier: (event) => event.locals.user?.id.toString() || getClientIdentifier(event) },
    validateInput: options.validateInput,
    checkSuspiciousContent: true,
    auditLog: true,
    csrfProtection: true,
    ...options
  })
};

// Extend RequestEvent locals type
declare global {
  namespace App {
    interface Locals {
      user?: {
        id: number;
        username: string;
        displayName: string;
        email: string;
        role: string;
        status: string;
      };
      validatedData?: any;
    }
  }
}