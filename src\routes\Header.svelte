<script lang="ts">
	import { page } from '$app/stores';
	import { onMount } from 'svelte';
	import ThemeToggle from '$lib/components/ThemeToggle.svelte';

	// Placeholder for logo - we'll replace this with a real logo later
	let logoUrl = '/images/logo.png';

	// Message of the day
	let messageOfTheDay = '';
	let motdIndex = 0;
	let motdList = [
		'<PERSON> was born on December 23, 2002, in Vancouver, Canada.',
		'<PERSON> plays <PERSON> in the Netflix series Stranger Things.',
		'<PERSON> is the lead vocalist and guitarist for the rock band The Aubreys.',
		'<PERSON> made his directorial debut with the short film "Night Shifts" in 2020.',
		'<PERSON> starred as <PERSON> in the horror films "It" (2017) and "It Chapter Two" (2019).'
	];

	// Rotate message of the day
	function rotateMOTD() {
		motdIndex = (motdIndex + 1) % motdList.length;
		messageOfTheDay = motdList[motdIndex];
	}

	onMount(() => {
		// Set initial message
		messageOfTheDay = motdList[motdIndex];

		// Rotate message every 10 seconds
		const interval = setInterval(rotateMOTD, 10000);

		// Clean up interval on component destruction
		return () => clearInterval(interval);

		// In a real app, we would fetch these from the API
		// fetch('/api/motd').then(r => r.json()).then(data => {
		//   motdList = data;
		//   messageOfTheDay = motdList[0];
		// });
	});
</script>

<header>
	<div class="logo">
		<a href="/" aria-label="Finn Wolfhard Fan Club - Go to homepage">
			<img src={logoUrl} alt="Finn Wolfhard Fan Club logo" />
			<span class="site-name">Finn Wolfhard Fan Club</span>
		</a>
	</div>

	<div class="message-of-the-day" role="complementary" aria-label="Message of the day">
		<p aria-live="polite" aria-atomic="true">{messageOfTheDay}</p>
	</div>

	<nav id="main-navigation" aria-label="Main navigation">
		<ul>
			<li aria-current={$page.url.pathname === '/' ? 'page' : undefined}>
				<a href="/">Home</a>
			</li>
			<li aria-current={$page.url.pathname === '/gallery' ? 'page' : undefined}>
				<a href="/gallery">Gallery</a>
			</li>
			<li aria-current={$page.url.pathname === '/news' ? 'page' : undefined}>
				<a href="/news">News</a>
			</li>
			<li aria-current={$page.url.pathname.startsWith('/message-boards') ? 'page' : undefined}>
				<a href="/message-boards">Message Boards</a>
			</li>
			<li aria-current={$page.url.pathname === '/about' ? 'page' : undefined}>
				<a href="/about">About</a>
			</li>
			<li class="theme-toggle-item">
				<ThemeToggle />
			</li>
			<li class="login-button">
				<a href="/login" aria-label="Login to your account">Login</a>
			</li>
		</ul>
	</nav>
</header>

<style>
	header {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: var(--space-md);
		background-color: var(--color-nav-bg);
		border-bottom: var(--border-width-thin) solid var(--color-nav-border);
		color: var(--color-nav-text);
		transition: var(--transition-theme);
	}

	.logo {
		display: flex;
		align-items: center;
		margin-bottom: var(--space-sm);
	}

	.logo a {
		display: flex;
		align-items: center;
		text-decoration: none;
		color: var(--color-nav-text);
		transition: var(--transition-theme);
	}

	.logo img {
		width: var(--space-3xl);
		height: var(--space-3xl);
		margin-right: var(--space-sm);
		border-radius: var(--border-radius-full);
		object-fit: cover;
	}

	.site-name {
		font-size: var(--font-size-xl);
		font-weight: var(--font-weight-bold);
	}

	.message-of-the-day {
		width: 100%;
		text-align: center;
		padding: var(--space-sm);
		background-color: var(--color-surface-secondary);
		border-radius: var(--border-radius-md);
		margin: var(--space-sm) 0;
		font-style: italic;
		color: var(--color-text-secondary);
		transition: var(--transition-theme);
	}

	.message-of-the-day p {
		margin: 0;
	}

	nav {
		width: 100%;
	}

	ul {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		align-items: center;
		padding: 0;
		margin: 0;
		list-style: none;
		gap: var(--space-sm);
	}

	li {
		position: relative;
	}

	li[aria-current='page']::after {
		content: '';
		position: absolute;
		bottom: calc(-1 * var(--space-xs));
		left: 0;
		width: 100%;
		height: var(--border-width-medium);
		background-color: var(--color-interactive-primary);
	}

	nav a {
		display: inline-block;
		padding: var(--space-sm) var(--space-xs);
		color: var(--color-nav-text);
		font-weight: var(--font-weight-semibold);
		text-decoration: none;
		transition: var(--transition-theme);
	}

	nav a:hover {
		color: var(--color-nav-text-hover);
	}

	.theme-toggle-item {
		display: flex;
		align-items: center;
	}

	.login-button a {
		background-color: var(--color-button-primary-bg);
		color: var(--color-button-primary-text);
		padding: var(--space-sm) var(--space-md);
		border-radius: var(--border-radius-md);
		border: var(--border-width-thin) solid var(--color-button-primary-border);
		transition: var(--transition-theme);
	}

	.login-button a:hover {
		background-color: var(--color-button-primary-hover-bg);
		border-color: var(--color-button-primary-hover-border);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	@media (min-width: 768px) {
		header {
			flex-direction: row;
			justify-content: space-between;
		}

		.logo {
			margin-bottom: 0;
		}

		.message-of-the-day {
			width: auto;
			flex: 1;
			margin: 0 var(--space-md);
		}

		nav {
			width: auto;
		}
	}
</style>
