import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, cleanup } from '@testing-library/svelte';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';

import CreateTopicModal from '$lib/components/CreateTopicModal.svelte';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

describe('CreateTopicModal Component', () => {
  beforeEach(() => {
    document.body.innerHTML = '';
  });

  afterEach(() => {
    cleanup();
  });

  describe('Rendering', () => {
    it('should not render when isOpen is false', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: false,
          boardName: 'Test Board'
        }
      });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('should render when isOpen is true', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('Create New Topic')).toBeInTheDocument();
      expect(screen.getByText(/Start a new discussion in.*Test Board/)).toBeInTheDocument();
    });

    it('should render all form fields', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      expect(screen.getByLabelText(/Topic Title/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Description/)).toBeInTheDocument();
      expect(screen.getByLabelText(/Content/)).toBeInTheDocument();
    });

    it('should render action buttons', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      expect(screen.getByRole('button', { name: /Cancel/ })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /Create Topic/ })).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const dialog = screen.getByRole('dialog');
      expect(dialog).toHaveAttribute('aria-modal', 'true');
      expect(dialog).toHaveAttribute('aria-labelledby', 'modal-title');
    });

    it('should have proper form labels and associations', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      const descriptionInput = screen.getByLabelText(/Description/);
      const contentInput = screen.getByLabelText(/Content/);

      expect(titleInput).toHaveAttribute('id', 'topic-title');
      expect(descriptionInput).toHaveAttribute('id', 'topic-description');
      expect(contentInput).toHaveAttribute('id', 'topic-content');
    });

    it('should have no accessibility violations', async () => {
      const { container } = render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('should support keyboard navigation', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      const closeButton = screen.getByRole('button', { name: /Close modal/ });

      // Title input should be focusable
      titleInput.focus();
      expect(document.activeElement).toBe(titleInput);

      // Close button should be focusable
      closeButton.focus();
      expect(document.activeElement).toBe(closeButton);
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for empty required fields', async () => {
      const mockSubmit = vi.fn();
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const component = screen.getByRole('dialog').closest('div');
      component?.addEventListener('submit', mockSubmit);

      const submitButton = screen.getByRole('button', { name: /Create Topic/ });
      fireEvent.click(submitButton);

      // Should show validation errors
      expect(screen.getByText('Title is required')).toBeInTheDocument();
      expect(screen.getByText('Content is required')).toBeInTheDocument();
    });

    it('should validate minimum title length', async () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      fireEvent.input(titleInput, { target: { value: 'ab' } });

      const submitButton = screen.getByRole('button', { name: /Create Topic/ });
      fireEvent.click(submitButton);

      expect(screen.getByText('Title must be at least 3 characters long')).toBeInTheDocument();
    });

    it('should validate maximum title length', async () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      const longTitle = 'a'.repeat(201);
      fireEvent.input(titleInput, { target: { value: longTitle } });

      const submitButton = screen.getByRole('button', { name: /Create Topic/ });
      fireEvent.click(submitButton);

      expect(screen.getByText('Title must be less than 200 characters')).toBeInTheDocument();
    });

    it('should validate minimum content length', async () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      const contentInput = screen.getByLabelText(/Content/);
      
      fireEvent.input(titleInput, { target: { value: 'Valid Title' } });
      fireEvent.input(contentInput, { target: { value: 'short' } });

      const submitButton = screen.getByRole('button', { name: /Create Topic/ });
      fireEvent.click(submitButton);

      expect(screen.getByText('Content must be at least 10 characters long')).toBeInTheDocument();
    });

    it('should validate maximum description length', async () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const descriptionInput = screen.getByLabelText(/Description/);
      const longDescription = 'a'.repeat(501);
      fireEvent.input(descriptionInput, { target: { value: longDescription } });

      const submitButton = screen.getByRole('button', { name: /Create Topic/ });
      fireEvent.click(submitButton);

      expect(screen.getByText('Description must be less than 500 characters')).toBeInTheDocument();
    });
  });

  describe('Character Counters', () => {
    it('should display character count for title', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      fireEvent.input(titleInput, { target: { value: 'Test Title' } });

      expect(screen.getByText('10/200 characters')).toBeInTheDocument();
    });

    it('should display character count for description', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const descriptionInput = screen.getByLabelText(/Description/);
      fireEvent.input(descriptionInput, { target: { value: 'Test Description' } });

      expect(screen.getByText('16/500 characters')).toBeInTheDocument();
    });

    it('should display character count for content', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      const contentInput = screen.getByLabelText(/Content/);
      fireEvent.input(contentInput, { target: { value: 'Test content for the topic' } });

      expect(screen.getByText('26 characters')).toBeInTheDocument();
    });
  });

  describe('Event Handling', () => {
    it('should emit submit event with valid data', async () => {
      const mockSubmit = vi.fn();
      const component = render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      component.component.$on('submit', mockSubmit);

      const titleInput = screen.getByLabelText(/Topic Title/);
      const contentInput = screen.getByLabelText(/Content/);
      
      fireEvent.input(titleInput, { target: { value: 'Valid Title' } });
      fireEvent.input(contentInput, { target: { value: 'Valid content that is long enough' } });

      const submitButton = screen.getByRole('button', { name: /Create Topic/ });
      fireEvent.click(submitButton);

      expect(mockSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          detail: {
            title: 'Valid Title',
            description: null,
            content: 'Valid content that is long enough'
          }
        })
      );
    });

    it('should emit cancel event when cancel button is clicked', () => {
      const mockCancel = vi.fn();
      const component = render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      component.component.$on('cancel', mockCancel);

      const cancelButton = screen.getByRole('button', { name: /Cancel/ });
      fireEvent.click(cancelButton);

      expect(mockCancel).toHaveBeenCalled();
    });

    it('should emit cancel event when close button is clicked', () => {
      const mockCancel = vi.fn();
      const component = render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      component.component.$on('cancel', mockCancel);

      const closeButton = screen.getByRole('button', { name: /Close modal/ });
      fireEvent.click(closeButton);

      expect(mockCancel).toHaveBeenCalled();
    });

    it('should emit cancel event when Escape key is pressed', () => {
      const mockCancel = vi.fn();
      const component = render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board'
        }
      });

      component.component.$on('cancel', mockCancel);

      fireEvent.keyDown(window, { key: 'Escape' });

      expect(mockCancel).toHaveBeenCalled();
    });
  });

  describe('Loading State', () => {
    it('should disable form elements when loading', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board',
          isLoading: true
        }
      });

      const titleInput = screen.getByLabelText(/Topic Title/);
      const descriptionInput = screen.getByLabelText(/Description/);
      const contentInput = screen.getByLabelText(/Content/);
      const submitButton = screen.getByRole('button', { name: /Creating.../ });
      const cancelButton = screen.getByRole('button', { name: /Cancel/ });
      const closeButton = screen.getByRole('button', { name: /Close modal/ });

      expect(titleInput).toBeDisabled();
      expect(descriptionInput).toBeDisabled();
      expect(contentInput).toBeDisabled();
      expect(submitButton).toBeDisabled();
      expect(cancelButton).toBeDisabled();
      expect(closeButton).toBeDisabled();
    });

    it('should show loading text on submit button when loading', () => {
      render(CreateTopicModal, {
        props: {
          isOpen: true,
          boardName: 'Test Board',
          isLoading: true
        }
      });

      expect(screen.getByRole('button', { name: /Creating.../ })).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /Create Topic/ })).not.toBeInTheDocument();
    });
  });
});
