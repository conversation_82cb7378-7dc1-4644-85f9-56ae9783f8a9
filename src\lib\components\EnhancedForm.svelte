<script lang="ts">
	import { createEventDispatcher, onMount } from 'svelte';
	import { z } from 'zod';
	import ErrorMessage from './ErrorMessage.svelte';
	import LoadingSpinner from './LoadingSpinner.svelte';
	import AccessibilityAnnouncer from './AccessibilityAnnouncer.svelte';

	/**
	 * Enhanced Form Component with comprehensive validation and accessibility
	 * Implements WCAG 2.1 AA compliance and security best practices
	 */

	// Props
	export let schema: z.ZodSchema<any>;
	export let initialData: Record<string, any> = {};
	export let submitHandler: (data: any) => Promise<any>;
	export let title = '';
	export let description = '';
	export let submitLabel = 'Submit';
	export let resetLabel = 'Reset';
	export let showReset = true;
	export let validateOnChange = true;
	export let validateOnBlur = true;
	export let disabled = false;
	export let ariaLabel = '';
	export let ariaDescribedBy = '';

	// State
	let formData = { ...initialData };
	let errors: Record<string, string[]> = {};
	let fieldErrors: Record<string, string> = {};
	let isSubmitting = false;
	let isValid = false;
	let hasBeenSubmitted = false;
	let announcement = '';
	let formElement: HTMLFormElement;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		submit: { data: any; isValid: boolean };
		change: { field: string; value: any; isValid: boolean };
		error: { errors: Record<string, string[]> };
		success: { data: any };
		reset: void;
	}>();

	// Reactive validation
	$: {
		validateForm();
	}

	// Form validation function
	function validateForm() {
		try {
			const result = schema.safeParse(formData);
			
			if (result.success) {
				errors = {};
				fieldErrors = {};
				isValid = true;
			} else {
				// Process Zod errors
				const newErrors: Record<string, string[]> = {};
				const newFieldErrors: Record<string, string> = {};
				
				result.error.errors.forEach(error => {
					const path = error.path.join('.');
					if (!newErrors[path]) {
						newErrors[path] = [];
					}
					newErrors[path].push(error.message);
					
					// Store first error for each field for display
					if (!newFieldErrors[path]) {
						newFieldErrors[path] = error.message;
					}
				});
				
				errors = newErrors;
				fieldErrors = newFieldErrors;
				isValid = false;
			}
		} catch (error) {
			console.error('Form validation error:', error);
			isValid = false;
		}
	}

	// Field change handler
	function handleFieldChange(field: string, value: any) {
		formData[field] = value;
		
		// Validate on change if enabled
		if (validateOnChange && hasBeenSubmitted) {
			validateForm();
		}
		
		dispatch('change', { field, value, isValid });
	}

	// Field blur handler
	function handleFieldBlur(field: string) {
		if (validateOnBlur) {
			validateForm();
		}
	}

	// Form submission handler
	async function handleSubmit(event: Event) {
		event.preventDefault();
		
		hasBeenSubmitted = true;
		validateForm();
		
		if (!isValid) {
			// Focus first error field
			const firstErrorField = Object.keys(fieldErrors)[0];
			if (firstErrorField) {
				const errorElement = formElement.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
				if (errorElement) {
					errorElement.focus();
					announcement = `Form has ${Object.keys(fieldErrors).length} validation errors. Please correct them and try again.`;
				}
			}
			
			dispatch('error', { errors });
			return;
		}
		
		if (disabled || isSubmitting) {
			return;
		}
		
		isSubmitting = true;
		announcement = 'Submitting form...';
		
		try {
			const result = await submitHandler(formData);
			announcement = 'Form submitted successfully!';
			dispatch('success', { data: result });
		} catch (error) {
			console.error('Form submission error:', error);
			announcement = 'Form submission failed. Please try again.';
			dispatch('error', { errors: { _form: [(error as Error).message] } });
		} finally {
			isSubmitting = false;
		}
	}

	// Reset form handler
	function handleReset() {
		formData = { ...initialData };
		errors = {};
		fieldErrors = {};
		hasBeenSubmitted = false;
		isValid = false;
		announcement = 'Form has been reset.';
		dispatch('reset');
	}

	// Keyboard navigation handler
	function handleKeyDown(event: KeyboardEvent) {
		// Submit on Ctrl+Enter
		if (event.ctrlKey && event.key === 'Enter') {
			event.preventDefault();
			handleSubmit(event);
		}
		
		// Reset on Ctrl+R (if not disabled)
		if (event.ctrlKey && event.key === 'r' && showReset) {
			event.preventDefault();
			handleReset();
		}
	}

	// Auto-focus first field on mount
	onMount(() => {
		const firstInput = formElement.querySelector('input, select, textarea') as HTMLElement;
		if (firstInput) {
			firstInput.focus();
		}
	});

	// Generate unique IDs for accessibility
	const formId = `form-${Math.random().toString(36).substr(2, 9)}`;
	const titleId = `${formId}-title`;
	const descriptionId = `${formId}-description`;
	const errorsId = `${formId}-errors`;
</script>

<AccessibilityAnnouncer message={announcement} priority="polite" />

<form
	bind:this={formElement}
	on:submit={handleSubmit}
	on:keydown={handleKeyDown}
	class="enhanced-form"
	class:has-errors={Object.keys(fieldErrors).length > 0}
	class:is-submitting={isSubmitting}
	novalidate
	aria-label={ariaLabel || title}
	aria-describedby={[descriptionId, errorsId, ariaDescribedBy].filter(Boolean).join(' ')}
	role="form"
>
	{#if title}
		<h2 id={titleId} class="form-title">
			{title}
		</h2>
	{/if}

	{#if description}
		<p id={descriptionId} class="form-description">
			{description}
		</p>
	{/if}

	<!-- Global form errors -->
	{#if errors._form}
		<div id={errorsId} class="form-errors" role="alert" aria-live="assertive">
			<ErrorMessage
				title="Form Error"
				message={errors._form.join(', ')}
				type="error"
			/>
		</div>
	{/if}

	<!-- Form fields slot -->
	<div class="form-fields" role="group" aria-label="Form fields">
		<slot 
			{formData} 
			{fieldErrors} 
			{handleFieldChange} 
			{handleFieldBlur}
			{isSubmitting}
			{disabled}
		/>
	</div>

	<!-- Form actions -->
	<div class="form-actions" role="group" aria-label="Form actions">
		<button
			type="submit"
			class="submit-button"
			disabled={disabled || isSubmitting}
			aria-describedby="submit-help"
		>
			{#if isSubmitting}
				<LoadingSpinner size="small" inline message="" />
				Submitting...
			{:else}
				{submitLabel}
			{/if}
		</button>
		<div id="submit-help" class="sr-only">
			{isSubmitting ? 'Form is being submitted' : 'Submit the form'}
		</div>

		{#if showReset}
			<button
				type="button"
				class="reset-button"
				on:click={handleReset}
				disabled={disabled || isSubmitting}
				aria-describedby="reset-help"
			>
				{resetLabel}
			</button>
			<div id="reset-help" class="sr-only">
				Reset all form fields to their initial values
			</div>
		{/if}
	</div>

	<!-- Form status for screen readers -->
	<div class="sr-only" aria-live="polite" aria-atomic="true">
		{#if isValid && hasBeenSubmitted}
			Form is valid and ready to submit
		{:else if hasBeenSubmitted && Object.keys(fieldErrors).length > 0}
			Form has {Object.keys(fieldErrors).length} validation errors
		{/if}
	</div>

	<!-- Keyboard shortcuts help -->
	<div class="form-help" role="region" aria-label="Keyboard shortcuts">
		<details>
			<summary>Keyboard Shortcuts</summary>
			<ul>
				<li><kbd>Ctrl</kbd> + <kbd>Enter</kbd>: Submit form</li>
				{#if showReset}
					<li><kbd>Ctrl</kbd> + <kbd>R</kbd>: Reset form</li>
				{/if}
				<li><kbd>Tab</kbd>: Navigate between fields</li>
				<li><kbd>Shift</kbd> + <kbd>Tab</kbd>: Navigate backwards</li>
			</ul>
		</details>
	</div>
</form>

<style>
	.enhanced-form {
		background: var(--color-surface-primary);
		border: 1px solid var(--color-border-secondary);
		border-radius: var(--border-radius-lg);
		padding: var(--spacing-xl);
		max-width: 100%;
		box-shadow: var(--shadow-sm);
		transition: var(--transition-theme);
	}

	.enhanced-form:focus-within {
		border-color: var(--color-border-focus);
		box-shadow: var(--shadow-md);
	}

	.form-title {
		color: var(--color-text-primary);
		font-size: var(--font-size-2xl);
		font-weight: 600;
		margin: 0 0 var(--spacing-md) 0;
		line-height: var(--line-height-tight);
	}

	.form-description {
		color: var(--color-text-secondary);
		font-size: var(--font-size-md);
		line-height: var(--line-height-normal);
		margin: 0 0 var(--spacing-lg) 0;
	}

	.form-errors {
		margin-bottom: var(--spacing-lg);
	}

	.form-fields {
		margin-bottom: var(--spacing-lg);
	}

	.form-actions {
		display: flex;
		gap: var(--spacing-md);
		align-items: center;
		flex-wrap: wrap;
		padding-top: var(--spacing-md);
		border-top: 1px solid var(--color-border-secondary);
	}

	.submit-button {
		background: var(--color-interactive-primary);
		color: var(--color-text-inverse);
		border: 1px solid var(--color-interactive-primary);
		padding: var(--spacing-sm) var(--spacing-lg);
		border-radius: var(--border-radius-md);
		font-weight: 500;
		font-size: var(--font-size-md);
		cursor: pointer;
		transition: var(--transition-fast);
		display: inline-flex;
		align-items: center;
		gap: var(--spacing-sm);
		min-height: 44px; /* WCAG touch target size */
	}

	.submit-button:hover:not(:disabled) {
		background: var(--color-interactive-primary-hover);
		border-color: var(--color-interactive-primary-hover);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.submit-button:focus {
		outline: 2px solid var(--color-border-focus);
		outline-offset: 2px;
	}

	.submit-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}

	.reset-button {
		background: var(--color-surface-secondary);
		color: var(--color-text-primary);
		border: 1px solid var(--color-border-primary);
		padding: var(--spacing-sm) var(--spacing-lg);
		border-radius: var(--border-radius-md);
		font-weight: 500;
		font-size: var(--font-size-md);
		cursor: pointer;
		transition: var(--transition-fast);
		min-height: 44px; /* WCAG touch target size */
	}

	.reset-button:hover:not(:disabled) {
		background: var(--color-surface-tertiary);
		border-color: var(--color-border-focus);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	.reset-button:focus {
		outline: 2px solid var(--color-border-focus);
		outline-offset: 2px;
	}

	.reset-button:disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;
		box-shadow: none;
	}

	.form-help {
		margin-top: var(--spacing-lg);
		padding-top: var(--spacing-md);
		border-top: 1px solid var(--color-border-tertiary);
	}

	.form-help details {
		color: var(--color-text-secondary);
		font-size: var(--font-size-sm);
	}

	.form-help summary {
		cursor: pointer;
		font-weight: 500;
		padding: var(--spacing-xs) 0;
		color: var(--color-interactive-primary);
	}

	.form-help summary:hover {
		color: var(--color-interactive-primary-hover);
	}

	.form-help summary:focus {
		outline: 2px solid var(--color-border-focus);
		outline-offset: 1px;
		border-radius: var(--border-radius-sm);
	}

	.form-help ul {
		margin: var(--spacing-sm) 0 0 var(--spacing-md);
		padding: 0;
	}

	.form-help li {
		margin-bottom: var(--spacing-xs);
		line-height: var(--line-height-normal);
	}

	.form-help kbd {
		background: var(--color-surface-tertiary);
		border: 1px solid var(--color-border-primary);
		border-radius: var(--border-radius-sm);
		padding: 0.125rem 0.25rem;
		font-size: 0.75rem;
		font-family: monospace;
		color: var(--color-text-primary);
	}

	.sr-only {
		position: absolute;
		width: 1px;
		height: 1px;
		padding: 0;
		margin: -1px;
		overflow: hidden;
		clip: rect(0, 0, 0, 0);
		white-space: nowrap;
		border: 0;
	}

	/* Form state indicators */
	.has-errors {
		border-color: var(--color-error);
	}

	.is-submitting {
		opacity: 0.8;
		pointer-events: none;
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.enhanced-form {
			border-width: 2px;
		}
		
		.submit-button,
		.reset-button {
			border-width: 2px;
			font-weight: 600;
		}
		
		.submit-button:focus,
		.reset-button:focus {
			outline-width: 3px;
		}
	}

	/* Reduced motion support */
	@media (prefers-reduced-motion: reduce) {
		.submit-button,
		.reset-button {
			transition: none;
			transform: none !important;
		}
		
		.enhanced-form {
			transition: none;
		}
	}

	/* Mobile responsive */
	@media (max-width: 640px) {
		.enhanced-form {
			padding: var(--spacing-lg);
		}
		
		.form-actions {
			flex-direction: column;
			align-items: stretch;
		}
		
		.submit-button,
		.reset-button {
			width: 100%;
			justify-content: center;
		}
	}
</style>