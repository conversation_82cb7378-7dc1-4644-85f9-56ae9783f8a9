<script lang="ts">
	export let links: Array<{ href: string; label: string }> = [
		{ href: '#main-content', label: 'Skip to main content' },
		{ href: '#navigation', label: 'Skip to navigation' },
		{ href: '#footer', label: 'Skip to footer' }
	];
</script>

<div class="skip-links" aria-label="Skip navigation links">
	{#each links as link}
		<a 
			href={link.href} 
			class="skip-link"
			on:click={(e) => {
				// Ensure focus moves to target element
				const target = document.querySelector(link.href);
				if (target) {
					target.focus();
					// Add tabindex if element isn't naturally focusable
					if (!target.hasAttribute('tabindex')) {
						target.setAttribute('tabindex', '-1');
					}
				}
			}}
		>
			{link.label}
		</a>
	{/each}
</div>

<style>
	.skip-links {
		position: absolute;
		top: -100px;
		left: 0;
		z-index: 1000;
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
		padding: 0.5rem;
	}

	.skip-link {
		background: var(--color-bg-primary);
		color: var(--color-text-primary);
		padding: 0.75rem 1rem;
		text-decoration: none;
		border: 2px solid var(--color-interactive-primary);
		border-radius: var(--border-radius-md);
		font-weight: 600;
		white-space: nowrap;
		transform: translateY(-100px);
		transition: transform 0.2s ease-in-out;
	}

	.skip-link:focus {
		transform: translateY(100px);
		outline: 3px solid var(--color-interactive-primary);
		outline-offset: 2px;
	}

	.skip-link:hover {
		background: var(--color-interactive-primary);
		color: var(--color-bg-primary);
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.skip-link {
			border-width: 3px;
			font-weight: 700;
		}
		
		.skip-link:focus {
			outline-width: 4px;
		}
	}
</style>