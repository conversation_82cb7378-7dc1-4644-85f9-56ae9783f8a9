# FWFC (Finn Wolfhard Fan Community) - Design Document

## 1. System Architecture

### 1.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Side   │    │   Server Side   │    │   Data Layer    │
│                 │    │                 │    │                 │
│ • SvelteKit App │◄──►│ • SvelteKit SSR │◄──►│ • SQLite DB     │
│ • Theme System  │    │ • API Routes    │    │ • File Storage  │
│ • Accessibility │    │ • Auth System   │    │ • Audit Logs    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 Technology Stack
- **Frontend**: SvelteKit + TypeScript + Tailwind CSS
- **Backend**: Node.js + SvelteKit API Routes
- **Database**: SQLite + Drizzle ORM
- **Storage**: File-based uploads
- **Deployment**: PM2 + Nginx + SSL

## 2. Database Design

### 2.1 Core Entity Relationships
```
Users ──┐
        ├── News Articles
        ├── Gallery Items
        ├── Message Board Posts
        └── Audit Logs

Boards ──┐
         ├── Topics ──── Posts
         └── Board Permissions

Content Authorship ──── Simulated Content
```

### 2.2 Key Tables Schema

#### Users Table
```sql
users (
  id INTEGER PRIMARY KEY,
  username TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  role TEXT DEFAULT 'user', -- admin, moderator, user
  status TEXT DEFAULT 'active',
  preferences JSON,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

#### Message Board System
```sql
boards (
  id INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  slug TEXT UNIQUE NOT NULL,
  icon TEXT, -- FontAwesome class
  color TEXT, -- Hex color
  position INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true
)

topics (
  id INTEGER PRIMARY KEY,
  board_id INTEGER REFERENCES boards(id),
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  author_id INTEGER REFERENCES users(id),
  is_pinned BOOLEAN DEFAULT false,
  is_locked BOOLEAN DEFAULT false,
  post_count INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0
)

board_permissions (
  id INTEGER PRIMARY KEY,
  board_id INTEGER REFERENCES boards(id),
  role TEXT, -- admin, moderator, user
  can_view BOOLEAN DEFAULT true,
  can_create_topics BOOLEAN DEFAULT true,
  can_reply BOOLEAN DEFAULT true,
  can_moderate BOOLEAN DEFAULT false
)
```

## 3. Theme System Design

### 3.1 CSS Custom Properties Architecture
The theme system uses CSS custom properties defined in `src/lib/styles/theme-tokens.css`:

```css
:root {
  /* Light theme (default) */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8fafc;
  --theme-text-primary: #1e293b;
  --theme-accent-primary: #3b82f6;
}

[data-theme="dark"] {
  /* Dark theme overrides */
  --theme-bg-primary: #0f172a;
  --theme-bg-secondary: #1e293b;
  --theme-text-primary: #f1f5f9;
  --theme-accent-primary: #60a5fa;
}
```

### 3.2 Theme Categories
- **Background Colors**: Primary, secondary, tertiary surfaces
- **Text Colors**: Primary, secondary, tertiary text hierarchy
- **Accent Colors**: Primary, secondary brand colors
- **Border Colors**: Various border weights and contexts
- **Status Colors**: Success, warning, error, info states

### 3.3 Theme Switching Implementation
```javascript
// Theme persistence in localStorage
const theme = localStorage.getItem('theme') || 'light';
document.documentElement.setAttribute('data-theme', theme);

// System preference detection
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
```

## 4. Component Architecture

### 4.1 Component Hierarchy
```
App.svelte
├── Header.svelte
├── Navigation.svelte
├── Main Content
│   ├── News Components
│   │   ├── NewsCard.svelte
│   │   ├── NewsDetail.svelte
│   │   └── AudioPlayer.svelte
│   ├── Gallery Components
│   │   ├── GalleryGrid.svelte
│   │   ├── ImageModal.svelte
│   │   └── ImageUploader.svelte
│   ├── Message Board Components
│   │   ├── BoardCard.svelte
│   │   ├── TopicList.svelte
│   │   ├── PostCard.svelte
│   │   └── CreateTopicModal.svelte
│   └── Admin Components
│       ├── AdminDashboard.svelte
│       ├── UserManagement.svelte
│       └── ContentModeration.svelte
├── Footer.svelte
└── Shared Components
    ├── LoadingSpinner.svelte
    ├── ErrorMessage.svelte
    ├── Modal.svelte
    └── Button.svelte
```

### 4.2 Reusable Component Design Patterns

#### Modal Component Pattern
```svelte
<script>
  export let isOpen = false;
  export let title = '';
  export let size = 'medium'; // small, medium, large
  
  // Accessibility and keyboard handling
  function handleKeydown(event) {
    if (event.key === 'Escape') close();
  }
</script>

{#if isOpen}
  <div class="modal-overlay" role="dialog" aria-modal="true">
    <div class="modal-content" class:size-{size}>
      <!-- Modal content -->
    </div>
  </div>
{/if}
```

#### Form Component Pattern
```svelte
<script>
  export let errors = {};
  export let isLoading = false;
  
  // Form validation and submission
  function validateForm() { /* validation logic */ }
  function handleSubmit(event) { /* submission logic */ }
</script>

<form on:submit|preventDefault={handleSubmit}>
  <!-- Form fields with error handling -->
</form>
```

## 5. API Design

### 5.1 RESTful API Structure
```
/api/
├── auth/
│   ├── login (POST)
│   ├── logout (POST)
│   └── register (POST)
├── news/
│   ├── / (GET, POST)
│   └── /[id] (GET, PUT, DELETE)
├── gallery/
│   ├── / (GET, POST)
│   └── /[id] (GET, PUT, DELETE)
├── message-boards/
│   ├── / (GET) - Public board listing
│   ├── /[slug] (GET) - Board topics
│   ├── /[slug]/topics (POST) - Create topic
│   └── /[boardSlug]/[topicSlug] (GET) - Topic posts
└── admin/
    ├── users/ (GET, POST, PUT, DELETE)
    ├── content/ (GET, POST, PUT, DELETE)
    └── audit-logs/ (GET)
```

### 5.2 API Response Format
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "version": "1.0.0"
  }
}
```

### 5.3 Error Response Format
```json
{
  "success": false,
  "error": "Human-readable error message",
  "code": "ERROR_CODE",
  "details": {
    // Additional error context
  }
}
```

## 6. Security Design

### 6.1 Authentication Flow
```
1. User submits credentials
2. Server validates against database
3. Session token generated and stored
4. Token sent as HTTP-only cookie
5. Subsequent requests include token
6. Server validates token on each request
```

### 6.2 Authorization Matrix
| Role | View Content | Create Content | Edit Own | Edit Others | Delete | Admin |
|------|-------------|----------------|----------|-------------|--------|-------|
| Anonymous | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| User | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| Moderator | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ |
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

### 6.3 Input Validation & Sanitization
- **Server-side validation** for all inputs
- **SQL injection prevention** via parameterized queries
- **XSS prevention** via content sanitization
- **File upload validation** with type and size limits
- **Rate limiting** on API endpoints

## 7. Accessibility Design

### 7.1 WCAG 2.1 AA Implementation
- **Semantic HTML** structure with proper landmarks
- **ARIA labels** and roles for complex interactions
- **Keyboard navigation** support for all interactive elements
- **Screen reader** compatibility with descriptive text
- **Color contrast** minimum 4.5:1 ratio
- **Focus management** with visible focus indicators

### 7.2 Accessibility Features
```svelte
<!-- Example: Accessible button -->
<button
  class="btn primary"
  aria-label="Create new topic in {boardName}"
  disabled={isLoading}
  on:click={handleClick}
>
  {#if isLoading}
    <span aria-hidden="true">⏳</span>
    <span class="sr-only">Creating topic...</span>
  {:else}
    <span aria-hidden="true">➕</span>
    New Topic
  {/if}
</button>
```

### 7.3 Responsive Design Breakpoints
```css
/* Mobile First Approach */
.component {
  /* Base mobile styles */
}

@media (min-width: 640px) {
  /* Small tablets */
}

@media (min-width: 768px) {
  /* Tablets */
}

@media (min-width: 1024px) {
  /* Desktop */
}

@media (min-width: 1280px) {
  /* Large desktop */
}
```

## 8. Performance Design

### 8.1 Optimization Strategies
- **Code splitting** with SvelteKit's automatic chunking
- **Image optimization** with responsive images and lazy loading
- **Database indexing** on frequently queried columns
- **Caching strategies** for static content and API responses
- **Progressive enhancement** for core functionality

### 8.2 Loading States & UX
```svelte
<!-- Loading state pattern -->
{#if isLoading}
  <LoadingSpinner />
  <p class="sr-only">Loading content...</p>
{:else if error}
  <ErrorMessage message={error} onRetry={retry} />
{:else}
  <!-- Content -->
{/if}
```

## 9. Content Management Design

### 9.1 Admin Interface Architecture
- **Dashboard overview** with key metrics
- **Content management** with CRUD operations
- **User management** with role assignment
- **Moderation tools** for content review
- **Audit logging** for all administrative actions

### 9.2 Content Workflow
```
1. Content Creation (Admin/Moderator)
2. Content Review (Optional AI + Manual)
3. Content Approval/Rejection
4. Content Publication
5. Content Monitoring & Moderation
```

## 10. Deployment Architecture

### 10.1 Production Environment
```
Internet → Nginx (SSL/Proxy) → SvelteKit App → SQLite DB
                ↓
        Static Files (uploads/)
```

### 10.2 Monitoring & Logging
- **Application logs** via PM2
- **Error tracking** with structured logging
- **Performance monitoring** with metrics collection
- **Uptime monitoring** with health checks
- **Security monitoring** with audit logs
